┌─────────────────────────────────────────────────────────────┐
│                    FRONTEND LAYER                           │
├─────────────────────────────────────────────────────────────┤
│ ChatInterface.tsx                                           │
│ ├─ UI Logic Only                                           │
│ ├─ Message Display                                         │
│ └─ Input Handling                                          │
├─────────────────────────────────────────────────────────────┤
│ CVPreview.tsx                                              │
│ ├─ Display Logic Only                                      │
│ └─ PDF Generation                                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   SERVICE LAYER                             │
├─────────────────────────────────────────────────────────────┤
│ chatService.ts (SINGLE SOURCE OF TRUTH)                    │
│ ├─ processUserInput()                                      │
│ ├─ extractUserProfile()                                    │
│ ├─ mergeProfileData()                                      │
│ ├─ getNextConversationState()                             │
│ ├─ isProfileComplete()                                     │
│ └─ validateUserInput()                                     │
├─────────────────────────────────────────────────────────────┤
│ profileService.ts                                          │
│ ├─ Profile validation                                      │
│ ├─ Data transformation                                     │
│ └─ Type safety                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   BACKEND LAYER                             │
├─────────────────────────────────────────────────────────────┤
│ supabase/functions/gemini-chat/index.ts                    │
│ ├─ AI API calls ONLY                                       │
│ ├─ Prompt generation                                       │
│ ├─ Response formatting                                     │
│ └─ Error handling                                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    SHARED TYPES                             │
├─────────────────────────────────────────────────────────────┤
│ types/UserProfile.ts                                       │
│ types/ConversationState.ts                                 │
│ types/ChatResponse.ts                                      │
└─────────────────────────────────────────────────────────────┘

✅ SINGLE SOURCE OF TRUTH:
├── 📄 profileService.ts      ← ALL profile operations
├── 📄 conversationService.ts ← ALL conversation logic
├── 📄 chatService.ts         ← ALL chat processing
└── 🗑️ geminiService.ts       ← REMOVED (748 lines eliminated)

✅ NO MORE DUPLICATIONS:
├── ChatInterface.tsx         ← UI only, uses centralized services
├── JobMatching.tsx          ← Fixed import, uses centralized services  
├── CVBuilder.tsx            ← Uses centralized services
└── Edge Function            ← AI API calls only