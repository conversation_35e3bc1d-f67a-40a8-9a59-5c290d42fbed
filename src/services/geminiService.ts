interface GeminiResponse {
  message: string;
  userProfile?: any;
  nextState?: string;
  isComplete?: boolean;
}

// Helper function to create profile summary for AI context
const createProfileSummary = (profile: any): string => {
  if (!profile || !profile.personalInfo) {
    return "No profile information collected yet";
  }

  const info = profile.personalInfo;
  const parts: string[] = [];

  if (info.fullName) parts.push(`Name: ${info.fullName}`);
  if (info.email) parts.push(`Email: ${info.email}`);
  if (info.phone) parts.push(`Phone: ${info.phone}`);
  if (info.address) parts.push(`Address: ${info.address}`);
  if (info.nationality) parts.push(`Nationality: ${info.nationality}`);

  if (profile.workExperience && profile.workExperience.length > 0) {
    parts.push(`Work Experience: ${profile.workExperience.length} job(s) recorded`);
  }

  if (profile.skills && profile.skills.length > 0) {
    parts.push(`Skills: ${profile.skills.length} skill(s) recorded`);
  }

  if (profile.languages && profile.languages.length > 0) {
    parts.push(`Languages: ${profile.languages.length} language(s) recorded`);
  }

  return parts.length > 0 ? parts.join(", ") : "No profile information collected yet";
};

// Helper function to create conversation context
const createConversationContext = (state: string, profile: any): string => {
  const info = profile?.personalInfo || {};

  switch (state) {
    case 'role_selection':
      return "Ask what type of hospitality job they want (waiter, chef, bartender, etc.)";

    case 'personal_info':
      const missing: string[] = [];
      if (!info.fullName) missing.push("full name");
      if (!info.email) missing.push("email address");
      if (!info.phone) missing.push("phone number");

      if (missing.length > 0) {
        return `Still need to collect: ${missing.join(", ")}. Ask for the FIRST missing item only.`;
      }
      return "All personal info collected - move to address/nationality";

    case 'contact_details':
      const missingContact: string[] = [];
      if (!info.address) missingContact.push("address in Germany");
      if (!info.nationality) missingContact.push("nationality");

      if (missingContact.length > 0) {
        return `Still need to collect: ${missingContact.join(", ")}. Ask for the FIRST missing item only.`;
      }
      return "All contact details collected - move to work experience";

    case 'work_experience':
      return "Collect details about their previous hospitality jobs (job title, company, duration, responsibilities)";

    case 'skills':
      return "Collect their relevant skills for hospitality work";

    case 'languages':
      return "Collect languages they speak and proficiency levels";

    case 'education':
      return "Collect their educational background";

    case 'certifications':
      return "Collect any relevant certificates or training";

    case 'completion':
      return "Congratulate them - their CV is complete!";

    default:
      return "Continue collecting CV information";
  }
};

const handleLocalResponse = (userInput: string, conversationState: string, currentProfile: any): GeminiResponse | null => {
  // More flexible local handling for critical validations only
  // Let AI handle most responses for better conversation flow

  if (conversationState === 'personal_info') {
    if (!currentProfile.personalInfo.fullName) {
      // More flexible name validation
      const nameWords = userInput.trim().split(/\s+/).filter(w => w.length > 0);
      if (nameWords.length >= 2 && nameWords.every(word => /^[a-zA-ZÀ-ÿ\u0100-\u017F\u0180-\u024F\u1E00-\u1EFF]+$/.test(word))) {
        const formattedName = nameWords.map(word =>
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
        return {
          message: `Perfect! Hello ${formattedName}! Now I need your email address so potential employers can contact you.`,
          userProfile: {
            personalInfo: { fullName: formattedName },
            workExperience: [],
            skills: [],
            languages: [],
            education: [],
            certifications: []
          },
          nextState: 'personal_info'
        };
      }
      // Let AI handle invalid names for better conversation
      return null;
    }

    if (!currentProfile.personalInfo.email) {
      // More flexible email validation
      const emailMatch = userInput.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
      if (emailMatch) {
        return {
          message: `Great! I have your email as ${emailMatch[0]}. Now I need your phone number so employers can call you directly.`,
          userProfile: {
            personalInfo: { email: emailMatch[0] },
            workExperience: [],
            skills: [],
            languages: [],
            education: [],
            certifications: []
          },
          nextState: 'personal_info'
        };
      }
      // Let AI handle invalid emails for better conversation
      return null;
    }

    if (!currentProfile.personalInfo.phone) {
      // More flexible phone validation
      const phoneMatch = userInput.match(/[\+]?[(]?[\d\s\-\(\)\.]{8,}/);
      if (phoneMatch) {
        return {
          message: `Perfect! Now I need your address in Germany. Please tell me your street address, city, and postal code.`,
          userProfile: {
            personalInfo: { phone: phoneMatch[0].trim() },
            workExperience: [],
            skills: [],
            languages: [],
            education: [],
            certifications: []
          },
          nextState: 'contact_details'
        };
      }
      // Let AI handle invalid phones for better conversation
      return null;
    }
  }

  // Only handle nationality if it's clearly identifiable
  if (conversationState === 'contact_details' && !currentProfile.personalInfo.nationality) {
    const nationalities = {
      'china': 'Chinese', 'chinese': 'Chinese',
      'german': 'German', 'germany': 'German', 'deutschland': 'German',
      'american': 'American', 'usa': 'American', 'united states': 'American',
      'british': 'British', 'uk': 'British', 'england': 'British',
      'spanish': 'Spanish', 'spain': 'Spanish',
      'french': 'French', 'france': 'French',
      'italian': 'Italian', 'italy': 'Italian',
      'turkish': 'Turkish', 'turkey': 'Turkish',
      'poland': 'Polish', 'polish': 'Polish',
      'ukrainian': 'Ukrainian', 'ukraine': 'Ukrainian',
      'syrian': 'Syrian', 'syria': 'Syrian',
      'afghan': 'Afghan', 'afghanistan': 'Afghan',
      'romanian': 'Romanian', 'romania': 'Romanian',
      'bulgaria': 'Bulgarian', 'bulgarian': 'Bulgarian',
      'indian': 'Indian', 'india': 'Indian',
      'pakistani': 'Pakistani', 'pakistan': 'Pakistani'
    };

    const inputLower = userInput.toLowerCase();
    const foundKey = Object.keys(nationalities).find(key => inputLower.includes(key));
    if (foundKey) {
      return {
        message: `Great! Now let's talk about your work experience. Tell me about your previous jobs in hotels, restaurants, or bars. What was your role and where did you work?`,
        userProfile: {
          personalInfo: { nationality: nationalities[foundKey as keyof typeof nationalities] },
          workExperience: [],
          skills: [],
          languages: [],
          education: [],
          certifications: []
        },
        nextState: 'work_experience'
      };
    }
  }

  // Let AI handle everything else for more natural conversation
  return null;
};

export const processUserInput = async (userInput: string, conversationState: string, currentProfile: any): Promise<GeminiResponse> => {
  try {
    const apiKey = localStorage.getItem('gemini_api_key');
    if (!apiKey) {
      throw new Error('API key not found');
    }

  // Create context-aware prompts that validate user input
  const prompts = {
    personal_info: createPersonalInfoPrompt(userInput, currentProfile),
    contact_details: createContactDetailsPrompt(userInput, currentProfile),
    work_experience: createWorkExperiencePrompt(userInput, currentProfile),
    skills: createSkillsPrompt(userInput, currentProfile),
    languages: createLanguagesPrompt(userInput, currentProfile),
    education: createEducationPrompt(userInput, currentProfile),
    certifications: createCertificationsPrompt(userInput, currentProfile),
    completion: `Thank the user for completing their profile. Let them know their CV is ready and they can now look for job matches. Keep response under 30 words.`
  };

  // Check if we should handle this locally to prevent duplicate questions
  const localResponse = handleLocalResponse(userInput, conversationState, currentProfile);
  if (localResponse) {
    return localResponse;
  }

    // Create comprehensive context for better AI responses
    const profileSummary = createProfileSummary(currentProfile);
    const conversationContext = createConversationContext(conversationState, currentProfile);

    const systemPrompt = `You are an experienced career counselor specializing in helping hospitality workers in Germany find jobs. You are warm, encouraging, and professional.

CONTEXT:
- Current conversation stage: ${conversationState}
- User's current profile: ${profileSummary}
- What you need to collect: ${conversationContext}
- User just said: "${userInput}"

PERSONALITY & STYLE:
- Be warm, encouraging, and supportive
- Use simple, clear language appropriate for hospitality workers
- Show genuine interest in their career goals
- Be patient and understanding with non-native speakers
- Never mention you are an AI or refer to technical terms

CONVERSATION RULES:
- Ask only ONE question at a time
- Build on what the user just said - acknowledge their input
- Don't repeat questions for information you already have
- If user gives incomplete info, ask for clarification gently
- If user goes off-topic, redirect kindly but firmly
- Validate and encourage good responses
- If user seems confused, rephrase your question differently

SPECIFIC TASK: ${prompts[conversationState as keyof typeof prompts] || prompts.personal_info}

Respond naturally as a helpful career counselor would, acknowledging what they said and asking the next appropriate question.`;

    // Try Gemini 2.0 Flash first, fallback to 1.5 Flash
    let modelName = 'gemini-2.0-flash-exp';
    let response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: systemPrompt
          }]
        }],
        generationConfig: {
          temperature: 0.3,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 200,
        }
      })
    });

    // If 2.0 fails, try 1.5 Flash
    if (!response.ok) {
      modelName = 'gemini-1.5-flash';
      response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: systemPrompt
            }]
          }],
          generationConfig: {
            temperature: 0.3,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 200,
          }
        })
      });
    }

    if (!response.ok) {
      throw new Error('Failed to get response from Gemini');
    }

    const data = await response.json();
    let aiMessage = data.candidates?.[0]?.content?.parts?.[0]?.text || "I am sorry, I could not understand that. Could you please try again?";
    
    // Filter out inappropriate AI responses
    if (aiMessage.toLowerCase().includes('large language model') || 
        aiMessage.toLowerCase().includes('i am an ai') ||
        aiMessage.toLowerCase().includes('google ai website') ||
        aiMessage.toLowerCase().includes('as an ai')) {
      aiMessage = "I am here to help you create your CV. Let us focus on getting your information ready for your job applications.";
    }
    
    // Clean up any remaining formatting
    aiMessage = aiMessage.replace(/^```json\s*/, '').replace(/\s*```$/, '').replace(/^```\s*/, '').replace(/\s*```$/, '');
    
    // Extract user profile data from the input
    const userProfile = extractUserProfile(userInput, conversationState, currentProfile);
    
    // Determine next state based on completeness
    const nextState = getNextConversationState(conversationState, userProfile, currentProfile);
    const isComplete = conversationState === 'completion';

    return {
      message: aiMessage,
      userProfile,
      nextState,
      isComplete
    };

  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
};

const createPersonalInfoPrompt = (userInput: string, currentProfile: any): string => {
  if (!currentProfile.personalInfo.fullName) {
    const nameWords = userInput.trim().split(' ').filter(w => w.length > 1);
    if (nameWords.length < 2) {
      return `You said "${userInput}" but I need your complete name with both first name and last name. Please tell me your full name so employers can contact you properly.`;
    }
    // Check if name is properly capitalized
    const hasProperCapitalization = nameWords.every(word => 
      word.charAt(0) === word.charAt(0).toUpperCase()
    );
    if (!hasProperCapitalization) {
      return `I need your name with proper capitalization. Please write your first name and last name with the first letter of each name capitalized (for example: John Smith).`;
    }
    return `Great! Now I need your email address so employers can contact you. What is your email address?`;
  } else if (!currentProfile.personalInfo.email) {
    const emailMatch = userInput.match(/\S+@\S+\.\S+/);
    if (!emailMatch) {
      return `I need a proper email <NAME_EMAIL> so employers can reach you. What is your email address?`;
    }
    return `Perfect! Now I need your phone number so employers can call you. What is your phone number?`;
  } else if (!currentProfile.personalInfo.phone) {
    const phoneMatch = userInput.match(/[\+]?[(]?[\d\s\-\(\)]{8,}/);
    if (!phoneMatch) {
      return `I need a complete phone number so employers can call you. Please give me your phone number with area code.`;
    }
    return `Thank you! Now I need your complete address in Germany where you live. Please tell me your street address, city, and postal code.`;
  }
  return `Got it! What country are you from? For example: Germany, Turkey, Spain, Poland, etc.`;
};

const createContactDetailsPrompt = (userInput: string, currentProfile: any): string => {
  if (!currentProfile.personalInfo.address) {
    if (userInput.length < 15 || (!userInput.toLowerCase().includes('str') && !userInput.toLowerCase().includes('straße') && 
        !userInput.toLowerCase().includes('germany') && !userInput.toLowerCase().includes('berlin') && 
        !userInput.toLowerCase().includes('munich') && !userInput.toLowerCase().includes('hamburg') &&
        !userInput.toLowerCase().includes('cologne') && !userInput.toLowerCase().includes('frankfurt'))) {
      return `I need your complete address in Germany including street name, house number, city and postal code. For example: "Hauptstraße 123, 10115 Berlin". What is your full address?`;
    }
    return `Great! What country are you from? Please tell me your nationality.`;
  } else if (!currentProfile.personalInfo.nationality) {
    const nationalities = {
      'china': 'Chinese',
      'chinese': 'Chinese',
      'german': 'German',
      'germany': 'German', 
      'american': 'American',
      'british': 'British',
      'spanish': 'Spanish',
      'french': 'French',
      'italian': 'Italian',
      'turkish': 'Turkish',
      'polish': 'Polish',
      'ukrainian': 'Ukrainian',
      'syrian': 'Syrian',
      'afghan': 'Afghan',
      'romanian': 'Romanian',
      'bulgarian': 'Bulgarian'
    };
    
    const inputLower = userInput.toLowerCase();
        const foundKey = Object.keys(nationalities).find(key => inputLower.includes(key));
        if (foundKey) {
          return `Great! Now let us talk about your work experience. Tell me about your previous jobs in hotels, restaurants, or bars. What job did you do and where did you work?`;
        }
  }
  return `Perfect! Now let us talk about your work experience. Tell me about your previous jobs in hotels, restaurants, or bars. What job did you do and where did you work?`;
};

const createWorkExperiencePrompt = (userInput: string, currentProfile: any): string => {
  if (userInput.length < 20) {
    return `I need more details about your work experience. Please tell me: What job did you have? Which restaurant, hotel, or bar did you work at? How long did you work there? For example: "I worked as a waiter at Restaurant ABC for 2 years from 2020 to 2022"`;
  }
  
  const hasJobTitle = userInput.toLowerCase().includes('waiter') || userInput.toLowerCase().includes('chef') || 
                     userInput.toLowerCase().includes('bartender') || userInput.toLowerCase().includes('cook') ||
                     userInput.toLowerCase().includes('server') || userInput.toLowerCase().includes('reception') ||
                     userInput.toLowerCase().includes('housekeeper') || userInput.toLowerCase().includes('cleaner');
  
  if (!hasJobTitle) {
    return `I need to know what job you had. Were you a waiter, chef, bartender, cook, hotel receptionist, housekeeper, or something else? Please tell me your job title and where you worked.`;
  }
  
  return `Excellent! Now tell me about your skills. What are you good at in your work? For example: serving customers, cooking food, making drinks, cleaning, handling money, working with people, etc.`;
};

const createSkillsPrompt = (userInput: string, currentProfile: any): string => {
  if (userInput.length < 15) {
    return `Please tell me more about your skills. What are you good at? For example: serving customers, cooking food, making drinks, cleaning, handling money, working in teams, etc. Tell me at least 3 things you are good at.`;
  }
  
  return `Great! Now tell me what languages you can speak and how well you speak them. For example: "I speak English very well, German at basic level, and Chinese is my native language."`;
};

const createLanguagesPrompt = (userInput: string, currentProfile: any): string => {
  const languages = ['english', 'german', 'turkish', 'arabic', 'spanish', 'french', 'polish', 'russian', 'romanian', 'bulgarian', 'chinese', 'mandarin'];
  const foundLanguage = languages.find(lang => userInput.toLowerCase().includes(lang));
  
  if (!foundLanguage) {
    return `Please tell me what languages you can speak. For example: English, German, Turkish, Arabic, Spanish, Polish, Chinese, etc. How well do you speak each language - basic level, good level, or very well?`;
  }
  
  return `Perfect! Now tell me about your education. What school did you finish? For example: high school, university, vocational training, etc. Where and when did you study?`;
};

const createEducationPrompt = (userInput: string, currentProfile: any): string => {
  if (userInput.length < 8) {
    return `Please tell me about your education. What is the highest level of school you completed? For example: high school, university degree, vocational training, apprenticeship, etc.`;
  }
  
  return `Great! Finally, do you have any certificates or special training for hospitality work? For example: food safety training, first aid certificate, bartending course, etc. If you do not have any, just say "no certificates".`;
};

const createCertificationsPrompt = (userInput: string, currentProfile: any): string => {
  return `Perfect! I have all the information I need to create your professional CV. Your CV is now complete and ready for job matching!`;
};

const extractUserProfile = (userInput: string, state: string, currentProfile: any): any => {
  const profile: any = {
    personalInfo: {},
    workExperience: [],
    skills: [],
    languages: [],
    education: [],
    certifications: []
  };

  console.log('Extracting profile for state:', state, 'Input:', userInput);

  switch (state) {
    case 'personal_info':
      if (!currentProfile.personalInfo.fullName) {
        const nameWords = userInput.trim().split(' ').filter(w => w.length > 1);
        if (nameWords.length >= 2) {
          // Capitalize first letter of each word
          const formattedName = nameWords.map(word => 
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          ).join(' ');
          profile.personalInfo.fullName = formattedName;
        }
      } else if (!currentProfile.personalInfo.email) {
        const emailMatch = userInput.match(/\S+@\S+\.\S+/);
        if (emailMatch) {
          profile.personalInfo.email = emailMatch[0];
        }
      } else if (!currentProfile.personalInfo.phone) {
        const phoneMatch = userInput.match(/[\+]?[(]?[\d\s\-\(\)]{8,}/);
        if (phoneMatch) {
          profile.personalInfo.phone = phoneMatch[0].trim();
        }
      }
      break;

    case 'contact_details':
      if (!currentProfile.personalInfo.address) {
        if (userInput.length > 15 && (userInput.toLowerCase().includes('str') || userInput.toLowerCase().includes('straße') || 
            userInput.toLowerCase().includes('berlin') || userInput.toLowerCase().includes('munich') ||
            userInput.toLowerCase().includes('germany') || userInput.toLowerCase().includes('hamburg') ||
            userInput.toLowerCase().includes('cologne') || userInput.toLowerCase().includes('frankfurt'))) {
          profile.personalInfo.address = userInput.trim();
        }
      } else if (!currentProfile.personalInfo.nationality) {
        const nationalities = {
          'china': 'Chinese',
          'chinese': 'Chinese',
          'german': 'German',
          'germany': 'German', 
          'american': 'American',
          'british': 'British',
          'spanish': 'Spanish',
          'french': 'French',
          'italian': 'Italian',
          'turkish': 'Turkish',
          'polish': 'Polish',
          'ukrainian': 'Ukrainian',
          'syrian': 'Syrian',
          'afghan': 'Afghan',
          'romanian': 'Romanian',
          'bulgarian': 'Bulgarian'
        };
        
        const inputLower = userInput.toLowerCase();
        const foundKey = Object.keys(nationalities).find(key => inputLower.includes(key));
        if (foundKey) {
          profile.personalInfo.nationality = nationalities[foundKey as keyof typeof nationalities];
        }
      }
      break;
      
    case 'work_experience':
      const lowerInput = userInput.toLowerCase();
      
      // Extract job title
      let jobTitle = 'Hospitality Professional';
      if (lowerInput.includes('bartender') || lowerInput.includes('bar tender')) {
        jobTitle = 'Bartender';
      } else if (lowerInput.includes('waiter') || lowerInput.includes('server') || lowerInput.includes('serving')) {
        jobTitle = 'Waiter/Server';
      } else if (lowerInput.includes('chef') || lowerInput.includes('cook')) {
        jobTitle = 'Chef/Cook';
      } else if (lowerInput.includes('reception') || lowerInput.includes('front desk')) {
        jobTitle = 'Hotel Receptionist';
      } else if (lowerInput.includes('housekeeper') || lowerInput.includes('cleaning')) {
        jobTitle = 'Housekeeper';
      }

      // Extract company name
      let company = 'Restaurant/Hotel';
      const companyMatch = userInput.match(/(?:at|in|for)\s+([A-Z][a-zA-Z\s]+(?:Hotel|Restaurant|Bar|Cafe|Resort|Club))/i);
      if (companyMatch) {
        company = companyMatch[1];
      }

      // Extract duration
      let duration = '2020-Present';
      const workYearMatch = userInput.match(/(\d{4})\s*[-to]*\s*(\d{4})?\s*(?:to present|present)?/i);
      if (workYearMatch) {
        duration = workYearMatch[2] ? `${workYearMatch[1]}-${workYearMatch[2]}` : `${workYearMatch[1]}-Present`;
      }
      
      profile.workExperience.push({
        jobTitle,
        company,
        duration,
        description: `Worked as ${jobTitle} providing excellent customer service and maintaining high standards of hospitality.`
      });
      break;
      
    case 'skills':
      const skillKeywords = {
        'Customer Service': ['customer', 'service', 'guest', 'serving'],
        'Communication': ['communication', 'talking', 'speaking', 'language'],
        'Teamwork': ['team', 'cooperation', 'together', 'group'],
        'Bartending': ['cocktail', 'drink', 'bar', 'mixing', 'alcohol'],
        'Cooking': ['cook', 'food', 'kitchen', 'prepare', 'recipe'],
        'Cash Handling': ['money', 'cash', 'payment', 'register', 'till'],
        'Cleaning': ['clean', 'tidy', 'sanitize', 'housekeeping'],
        'Multitasking': ['multitask', 'busy', 'many things', 'quick'],
        'Leadership': ['lead', 'manage', 'supervise', 'boss', 'train']
      };
      
      Object.entries(skillKeywords).forEach(([skill, keywords]) => {
        if (keywords.some(keyword => lowerInput.includes(keyword))) {
          let level = 'Intermediate';
          if (lowerInput.includes('very good') || lowerInput.includes('expert') || lowerInput.includes('excellent')) {
            level = 'Advanced';
          } else if (lowerInput.includes('basic') || lowerInput.includes('learning') || lowerInput.includes('beginner')) {
            level = 'Beginner';
          }
          profile.skills.push({ name: skill, level });
        }
      });
      break;
      
    case 'languages':
      const langKeywords = ['english', 'german', 'turkish', 'arabic', 'spanish', 'french', 'italian', 'polish', 'russian', 'romanian', 'bulgarian', 'chinese', 'mandarin'];
      const levelKeywords = {
        'Basic': ['basic', 'little', 'beginner', 'some'],
        'Conversational': ['good', 'okay', 'conversational', 'intermediate'],
        'Fluent': ['fluent', 'very good', 'excellent', 'very well', 'advanced'],
        'Native': ['native', 'mother tongue', 'first language']
      };

      langKeywords.forEach(lang => {
        if (lowerInput.includes(lang)) {
          let level = 'Conversational';
          Object.entries(levelKeywords).forEach(([levelName, keywords]) => {
            if (keywords.some(keyword => lowerInput.includes(keyword))) {
              level = levelName;
            }
          });
          
          let displayName = lang.charAt(0).toUpperCase() + lang.slice(1);
          if (lang === 'mandarin') displayName = 'Chinese';
          
          profile.languages.push({ 
            name: displayName, 
            level 
          });
        }
      });
      break;
      
    case 'education':
      let degree = 'High School';
      if (lowerInput.includes('university') || lowerInput.includes('bachelor') || lowerInput.includes('master')) {
        degree = 'University Degree';
      } else if (lowerInput.includes('vocational') || lowerInput.includes('training') || lowerInput.includes('apprentice')) {
        degree = 'Vocational Training';
      }

      const educationYearMatch = userInput.match(/(\d{4})/);
      profile.education.push({
        degree,
        institution: 'Educational Institution',
        year: educationYearMatch ? educationYearMatch[1] : '2020'
      });
      break;

    case 'certifications':
      if (!lowerInput.includes('no') && !lowerInput.includes('none')) {
        const certTypes = {
          'Food Safety': ['food safety', 'hygiene', 'haccp'],
          'First Aid': ['first aid', 'cpr', 'medical'],
          'Bartending': ['bartending', 'cocktail', 'drink'],
          'Language': ['language', 'german', 'english']
        };

        Object.entries(certTypes).forEach(([certName, keywords]) => {
          if (keywords.some(keyword => lowerInput.includes(keyword))) {
            profile.certifications.push({
              name: certName + ' Certificate',
              issuer: 'Certification Authority',
              year: new Date().getFullYear().toString()
            });
          }
        });
      }
      break;
  }

  console.log('Extracted profile:', profile);
  return profile;
};

const getNextConversationState = (currentState: string, extractedProfile: any, currentProfile: any): string => {
  // Merge current and extracted profiles to get complete picture
  const mergedProfile = mergeProfileData(currentProfile, extractedProfile);

  // Intelligent state progression based on what's actually missing
  const personalInfo = mergedProfile.personalInfo || {};

  // Personal info phase
  if (!personalInfo.fullName || !personalInfo.email || !personalInfo.phone) {
    return 'personal_info';
  }

  // Contact details phase
  if (!personalInfo.address || !personalInfo.nationality) {
    return 'contact_details';
  }

  // Work experience phase
  if (!mergedProfile.workExperience || mergedProfile.workExperience.length === 0) {
    return 'work_experience';
  }

  // Skills phase
  if (!mergedProfile.skills || mergedProfile.skills.length === 0) {
    return 'skills';
  }

  // Languages phase
  if (!mergedProfile.languages || mergedProfile.languages.length === 0) {
    return 'languages';
  }

  // Education phase
  if (!mergedProfile.education || mergedProfile.education.length === 0) {
    return 'education';
  }

  // Certifications phase (optional, can skip)
  if (currentState === 'education') {
    return 'certifications';
  }

  // Completion
  return 'completion';
};

// Helper function to merge profile data intelligently
const mergeProfileData = (currentProfile: any, newProfile: any): any => {
  if (!newProfile) return currentProfile;
  if (!currentProfile) return newProfile;

  return {
    personalInfo: {
      ...currentProfile.personalInfo,
      ...newProfile.personalInfo
    },
    workExperience: [
      ...(currentProfile.workExperience || []),
      ...(newProfile.workExperience || [])
    ],
    skills: [
      ...(currentProfile.skills || []),
      ...(newProfile.skills || [])
    ],
    languages: [
      ...(currentProfile.languages || []),
      ...(newProfile.languages || [])
    ],
    education: [
      ...(currentProfile.education || []),
      ...(newProfile.education || [])
    ],
    certifications: [
      ...(currentProfile.certifications || []),
      ...(newProfile.certifications || [])
    ]
  };
};
