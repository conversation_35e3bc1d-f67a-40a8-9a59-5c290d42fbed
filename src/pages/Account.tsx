
import React from 'react';
import Header from '@/components/Header';
import { User, Download, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';

const Account = () => {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header />
      
      <main className="container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Account Management
            </h1>
            <p className="text-gray-600">
              Manage your profile and CV history
            </p>
          </div>

          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200 mb-6">
            <div className="flex items-center gap-4 mb-6">
              <div className="bg-blue-100 p-3 rounded-full">
                <User className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Guest User</h2>
                <p className="text-gray-600">Currently using the platform as a guest</p>
              </div>
            </div>
            
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start gap-3">
                  <Download className="w-4 h-4" />
                  Download Last CV
                </Button>
                <Button variant="outline" className="w-full justify-start gap-3">
                  <Settings className="w-4 h-4" />
                  CV Preferences
                </Button>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 rounded-xl p-6 border border-blue-200">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Full Account Coming Soon
            </h3>
            <p className="text-blue-700 mb-4">
              We're working on user accounts with CV history, templates, and job matching. 
              Sign up for updates!
            </p>
            <Button className="bg-blue-600 hover:bg-blue-700">
              Join Waitlist
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Account;
