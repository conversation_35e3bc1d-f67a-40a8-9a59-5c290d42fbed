
import React from 'react';
import { Card } from '@/components/ui/card';
import { Bo<PERSON>, TrendingUp, Users, ArrowRight } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const ProcessDiagram = () => {
  const { t } = useLanguage();

  const steps = [
    {
      id: 1,
      title: t('process.step1.title'),
      description: t('process.step1.description'),
      icon: <Bot className="w-6 h-6" />,
      color: "bg-blue-500"
    },
    {
      id: 2,
      title: t('process.step2.title'),
      description: t('process.step2.description'),
      icon: <Users className="w-6 h-6" />,
      color: "bg-indigo-500"
    },
    {
      id: 3,
      title: t('process.step3.title'),
      description: t('process.step3.description'),
      icon: <TrendingUp className="w-6 h-6" />,
      color: "bg-purple-500"
    }
  ];

  return (
    <div className="py-16 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {t('process.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('process.description')}
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <div key={step.id} className="relative">
              <Card className="p-8 h-full bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-shadow">
                <div className="text-center">
                  <div className={`${step.color} w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6`}>
                    <div className="text-white">
                      {step.icon}
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </Card>
              
              {index < steps.length - 1 && (
                <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2 z-10">
                  <ArrowRight className="w-8 h-8 text-gray-400" />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProcessDiagram;
