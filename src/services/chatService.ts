
import { supabase } from '@/integrations/supabase/client';

export const processUserInputViaEdgeFunction = async (
  userInput: string,
  conversationState: string,
  currentProfile: any,
  conversationHistory?: Array<{content: string, isUser: boolean}>
) => {
  try {
    const { data, error } = await supabase.functions.invoke('gemini-chat', {
      body: {
        userInput,
        conversationState,
        currentProfile,
        conversationHistory: conversationHistory || []
      }
    });

    if (error) {
      throw error;
    }

    return {
      message: data.message,
      userProfile: data.userProfile,
      nextState: data.nextState
    };
  } catch (error) {
    console.error('Error calling chat service:', error);
    throw error;
  }
};
