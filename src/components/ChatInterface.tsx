import React, { useState, useRef, useEffect } from 'react';
import { Send, RotateCcw, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { UserProfile } from '@/types/UserProfile';
import { processUserInput } from '@/services/chatService';
import { mergeProfileData, isProfileComplete, createEmptyProfile } from '@/services/profileService';
import { useLanguage } from '@/contexts/LanguageContext';
import { getTemplateForRole } from '@/services/cvTemplateService';

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
}

interface ChatInterfaceProps {
  onProfileUpdate: (profile: UserProfile) => void;
  onChatCompletion: () => void;
  onTemplateSelect: (template: string) => void;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ onProfileUpdate, onChatCompletion, onTemplateSelect }) => {
  const { t } = useLanguage();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: "Hello! I'm here to help you create a professional CV for your hospitality job in Germany. Let's start - what type of hospitality job are you looking for? For example: Chef, Waiter, Bartender, Hotel Reception, or Housekeeper?",
      isUser: false,
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [conversationState, setConversationState] = useState('role_selection');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [userJobRole, setUserJobRole] = useState('');
  const [accumulatedProfile, setAccumulatedProfile] = useState<UserProfile>(createEmptyProfile());
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const determineTemplate = (role: string): string => {
    const lowerRole = role.toLowerCase();
    if (lowerRole.includes('chef') || lowerRole.includes('cook') || lowerRole.includes('kitchen')) {
      return 'professional';
    } else if (lowerRole.includes('waiter') || lowerRole.includes('bartender') || lowerRole.includes('server')) {
      return 'modern';
    } else {
      return 'classic';
    }
  };



  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    setInputValue('');
    setIsLoading(true);

    try {
      // Handle role selection first
      if (conversationState === 'role_selection') {
        const jobRole = currentInput.toLowerCase();
        const selectedCVTemplate = getTemplateForRole(jobRole);

        setSelectedTemplate(selectedCVTemplate.id);
        setUserJobRole(currentInput);
        onTemplateSelect(selectedCVTemplate.id);

        const botMessage: Message = {
          id: (Date.now() + 1).toString(),
          content: `Perfect! I've selected the "${selectedCVTemplate.name}" template which is ideal for ${currentInput} positions. This template features ${selectedCVTemplate.description.toLowerCase()}. Now let's create your professional CV! What is your full name? Please write your first name and last name.`,
          isUser: false,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, botMessage]);
        setConversationState('personal_info');
        setIsLoading(false);
        return;
      }

      // Use centralized chat processing
      const conversationHistory = messages.slice(-6).map(msg => ({
        content: msg.content,
        isUser: msg.isUser
      }));

      const response = await processUserInput(currentInput, conversationState, accumulatedProfile, conversationHistory);

      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.message,
        isUser: false,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);

      // Update profile if new data was extracted
      if (response.userProfile) {
        console.log('Response userProfile:', response.userProfile);
        console.log('Current accumulatedProfile before merge:', accumulatedProfile);

        const updatedProfile = mergeProfileData(accumulatedProfile, response.userProfile || {});
        console.log('Updated profile after merge:', updatedProfile);

        setAccumulatedProfile(updatedProfile);
        onProfileUpdate(updatedProfile);

        // Check if profile is complete
        if (response.nextState === 'completion' && isProfileComplete(updatedProfile)) {
          onChatCompletion();
          toast({
            title: "Resume Ready!",
            description: "Your resume is complete and ready for job matching.",
          });
        }
      }

      // Update conversation state
      if (response.nextState) {
        console.log('Setting conversation state:', response.nextState);
        setConversationState(response.nextState);
      }

    } catch (error) {
      console.error('Error processing message:', error);

      // Provide more helpful error messages based on the error type
      let errorContent = "I'm sorry, there was a problem processing your message. Please try again.";

      if (error instanceof Error) {
        if (error.message.includes('API key')) {
          errorContent = "It looks like there's an issue with the AI service configuration. Please try again in a moment, or contact support if the problem persists.";
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorContent = "I'm having trouble connecting to the AI service. Please check your internet connection and try again.";
        } else if (error.message.includes('rate limit')) {
          errorContent = "The AI service is currently busy. Please wait a moment and try again.";
        }
      }

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: errorContent,
        isUser: false,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);

      // Add a helpful suggestion
      setTimeout(() => {
        const suggestionMessage: Message = {
          id: (Date.now() + 2).toString(),
          content: "You can also try rephrasing your answer or use the reset button to start over if needed.",
          isUser: false,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, suggestionMessage]);
      }, 1000);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const resetChat = () => {
    setMessages([{
      id: '1',
      content: "Hello! I'm here to help you create a professional CV for your hospitality job in Germany. Let's start - what type of hospitality job are you looking for? For example: Chef, Waiter, Bartender, Hotel Reception, or Housekeeper?",
      isUser: false,
      timestamp: new Date()
    }]);
    setConversationState('role_selection');
    setSelectedTemplate('');
    setUserJobRole('');
    setAccumulatedProfile({
      personalInfo: { fullName: '', email: '', phone: '', address: '', nationality: '' },
      workExperience: [],
      skills: [],
      languages: [],
      education: [],
      certifications: []
    });
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 h-[600px] flex flex-col">
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-4 rounded-t-xl">
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full flex items-center justify-center text-xl">
              ✨
            </div>
            <div>
              <h3 className="font-semibold">{t('chat.title')}</h3>
              <p className="text-sm opacity-90">{t('chat.subtitle')}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={resetChat}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] p-3 rounded-lg ${
                message.isUser
                  ? 'bg-blue-600 text-white rounded-br-sm text-right'
                  : 'bg-gray-100 text-gray-900 rounded-bl-sm text-left'
              }`}
            >
              <div className="flex items-start gap-2">
                {!message.isUser && (
                  <div className="w-6 h-6 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full flex items-center justify-center text-sm mt-1 flex-shrink-0">
                    ✨
                  </div>
                )}
                <div className="flex-1">
                  <p className="text-sm">{message.content}</p>
                  <p className={`text-xs mt-1 opacity-70`}>
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
                {message.isUser && <User className="w-4 h-4 mt-1 flex-shrink-0" />}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 p-3 rounded-lg rounded-bl-sm max-w-[80%]">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full flex items-center justify-center text-sm">
                  ✨
                </div>
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="p-4 border-t border-gray-200">
        <div className="flex gap-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={t('chat.placeholder')}
            disabled={isLoading}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isLoading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
