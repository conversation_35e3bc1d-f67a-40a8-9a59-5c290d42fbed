import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { UserProfile } from '@/types/UserProfile';
import { Briefcase, MapPin, Clock, Star, Send, ArrowLeft, Eye, CheckSquare, Square, MessageSquare } from 'lucide-react';

interface JobMatch {
  id: string;
  title: string;
  company: string;
  location: string;
  salary: string;
  type: string;
  matchPercentage: number;
  description: string;
  requirements: string[];
  category: string;
}

interface JobMatchesProps {
  userProfile: UserProfile;
  selectedJobType: string;
  userJobRole?: string;
  onBackToChat: () => void;
}

const JobMatches: React.FC<JobMatchesProps> = ({ userProfile, selectedJobType, userJobRole = '', onBackToChat }) => {
  const [selectedJobs, setSelectedJobs] = useState<string[]>([]);
  const [showApplications, setShowApplications] = useState(false);
  const MAX_APPLICATIONS = 3;

  // Extended job database with better categorization
  const jobDatabase: JobMatch[] = [
    // Bartender Jobs
    {
      id: 'bar1',
      title: 'Bartender',
      company: 'Rooftop Bar Berlin',
      location: 'Berlin, Germany',
      salary: '€2,100 - €2,600/month',
      type: 'Full-time',
      matchPercentage: 95,
      description: 'Create amazing cocktails in Berlin\'s hottest rooftop location. Evening shifts with great tips.',
      requirements: ['Cocktail knowledge', 'Evening availability', 'German B1+'],
      category: 'bartender'
    },
    {
      id: 'bar2',
      title: 'Bar Manager',
      company: 'Luxury Hotel Hamburg',
      location: 'Hamburg, Germany',
      salary: '€3,000 - €3,500/month',
      type: 'Full-time',
      matchPercentage: 88,
      description: 'Manage our hotel bar operations. Oversee staff and create innovative drink menus.',
      requirements: ['Bar management experience', 'Staff leadership', 'German fluent'],
      category: 'bartender'
    },
    {
      id: 'bar3',
      title: 'Cocktail Bartender',
      company: 'Michelin Star Restaurant',
      location: 'Munich, Germany',
      salary: '€2,400 - €2,900/month',
      type: 'Full-time',
      matchPercentage: 92,
      description: 'Craft premium cocktails for fine dining guests. Work with sommelier team.',
      requirements: ['Fine cocktail experience', 'Wine knowledge', 'Attention to detail'],
      category: 'bartender'
    },
    // Server Jobs
    {
      id: 'server1',
      title: 'Restaurant Server',
      company: 'Hotel Adlon Berlin',
      location: 'Berlin, Germany',
      salary: '€2,200 - €2,800/month',
      type: 'Full-time',
      matchPercentage: 93,
      description: 'Serve international guests in our prestigious hotel restaurant.',
      requirements: ['Restaurant experience', 'German B1+', 'Customer service skills'],
      category: 'server'
    },
    {
      id: 'server2',
      title: 'Head Waiter',
      company: 'Traditional German Restaurant',
      location: 'Munich, Germany',
      salary: '€2,800 - €3,200/month',
      type: 'Full-time',
      matchPercentage: 89,
      description: 'Lead our service team in authentic Bavarian restaurant.',
      requirements: ['5+ years experience', 'Leadership skills', 'German knowledge'],
      category: 'server'
    },
    // Chef Jobs
    {
      id: 'chef1',
      title: 'Line Cook',
      company: 'Bavarian Brewery Restaurant',
      location: 'Munich, Germany',
      salary: '€2,000 - €2,500/month',
      type: 'Full-time',
      matchPercentage: 90,
      description: 'Prepare traditional German dishes in our busy kitchen.',
      requirements: ['Kitchen experience', 'Food safety knowledge', 'Team player'],
      category: 'chef'
    },
    {
      id: 'chef2',
      title: 'Sous Chef',
      company: 'International Hotel Chain',
      location: 'Frankfurt, Germany',
      salary: '€3,200 - €3,800/month',
      type: 'Full-time',
      matchPercentage: 87,
      description: 'Support head chef in international hotel kitchen operations.',
      requirements: ['Culinary degree', '3+ years experience', 'Leadership skills'],
      category: 'chef'
    },
    // Hotel Jobs
    {
      id: 'hotel1',
      title: 'Hotel Receptionist',
      company: 'Boutique Hotel Cologne',
      location: 'Cologne, Germany',
      salary: '€1,900 - €2,300/month',
      type: 'Full-time',
      matchPercentage: 85,
      description: 'Welcome guests and manage check-ins at charming boutique hotel.',
      requirements: ['Hotel experience', 'Multiple languages', 'Computer skills'],
      category: 'hotel'
    }
  ];

  // Smart job matching based on user profile and selected role
  const getJobMatches = (): JobMatch[] => {
    let filteredJobs = [...jobDatabase];

    // Filter by user's job role/category
    const userJobTitles = userProfile.workExperience.map(exp => exp.jobTitle.toLowerCase());
    console.log('User job titles:', userJobTitles);
    console.log('User job role:', userJobRole);

    // Determine primary category from user's experience or role selection
    let primaryCategory = '';
    if (userJobRole.toLowerCase().includes('bartender') || userJobTitles.some(title => title.includes('bartender'))) {
      primaryCategory = 'bartender';
    } else if (userJobRole.toLowerCase().includes('server') || userJobRole.toLowerCase().includes('waiter') || 
               userJobTitles.some(title => title.includes('server') || title.includes('waiter'))) {
      primaryCategory = 'server';
    } else if (userJobRole.toLowerCase().includes('chef') || userJobRole.toLowerCase().includes('cook') ||
               userJobTitles.some(title => title.includes('chef') || title.includes('cook'))) {
      primaryCategory = 'chef';
    } else if (userJobRole.toLowerCase().includes('hotel') || userJobRole.toLowerCase().includes('reception')) {
      primaryCategory = 'hotel';
    }

    console.log('Primary category determined:', primaryCategory);

    // Filter jobs by primary category, fallback to all if no specific category
    if (primaryCategory) {
      filteredJobs = filteredJobs.filter(job => job.category === primaryCategory);
    }

    // Boost match percentage based on relevant experience
    filteredJobs = filteredJobs.map(job => {
      let boostPercentage = 0;

      // Check job title matches
      if (userJobTitles.some(title => 
        title.includes(job.category) || job.title.toLowerCase().includes(title)
      )) {
        boostPercentage += 10;
      }

      // Check skills matches
      const userSkills = userProfile.skills.map(skill => skill.name.toLowerCase());
      const relevantSkills = userSkills.filter(skill => 
        job.requirements.some(req => req.toLowerCase().includes(skill))
      );
      boostPercentage += relevantSkills.length * 3;

      // Language bonus
      const hasGerman = userProfile.languages.some(lang => 
        lang.name.toLowerCase() === 'german'
      );
      if (hasGerman) boostPercentage += 5;

      return {
        ...job,
        matchPercentage: Math.min(98, job.matchPercentage + boostPercentage)
      };
    });

    // Sort by match percentage and return top matches
    return filteredJobs
      .sort((a, b) => b.matchPercentage - a.matchPercentage)
      .slice(0, 6);
  };

  const jobMatches = getJobMatches();

  // Get primary category for display
  const userJobTitles = userProfile.workExperience.map(exp => exp.jobTitle.toLowerCase());
  let primaryCategory = '';
  if (userJobRole.toLowerCase().includes('bartender') || userJobTitles.some(title => title.includes('bartender'))) {
    primaryCategory = 'bartender';
  } else if (userJobRole.toLowerCase().includes('server') || userJobRole.toLowerCase().includes('waiter') || 
             userJobTitles.some(title => title.includes('server') || title.includes('waiter'))) {
    primaryCategory = 'server';
  } else if (userJobRole.toLowerCase().includes('chef') || userJobRole.toLowerCase().includes('cook') ||
             userJobTitles.some(title => title.includes('chef') || title.includes('cook'))) {
    primaryCategory = 'chef';
  } else if (userJobRole.toLowerCase().includes('hotel') || userJobRole.toLowerCase().includes('reception')) {
    primaryCategory = 'hotel';
  }

  const handleJobSelect = (jobId: string) => {
    setSelectedJobs(prev => {
      if (prev.includes(jobId)) {
        return prev.filter(id => id !== jobId);
      } else if (prev.length < MAX_APPLICATIONS) {
        return [...prev, jobId];
      }
      return prev;
    });
  };

  const handleJobDetails = (jobId: string) => {
    const job = jobMatches.find(j => j.id === jobId);
    if (job) {
      // Store job data in localStorage for the details page
      localStorage.setItem('selectedJob', JSON.stringify(job));
      localStorage.setItem('userProfile', JSON.stringify(userProfile));
      window.location.href = '/job-details';
    }
  };

  const handleApplyToJobs = () => {
    setShowApplications(true);
  };

  const remainingApplications = MAX_APPLICATIONS - selectedJobs.length;

  if (showApplications) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-gradient-to-br from-green-100 to-emerald-100 p-8 rounded-xl shadow-lg border-2 border-green-200">
          <div className="text-center">
            <div className="bg-green-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Send className="w-10 h-10 text-green-700" />
            </div>
            <h2 className="text-3xl font-bold text-green-800 mb-4">Applications Sent!</h2>
            <p className="text-lg text-green-700 mb-6">
              Your applications have been sent to {selectedJobs.length} employers with personalized cover letters.
            </p>
            <p className="text-green-600 mb-8">
              Employers typically respond within 2-3 business days. We'll notify you of responses.
            </p>
            <div className="flex gap-4 justify-center">
              <Button 
                onClick={onBackToChat}
                variant="outline"
                className="border-indigo-400 text-indigo-700 hover:bg-indigo-50"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Review CV
              </Button>
              <Button 
                onClick={() => window.location.href = '/'}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Back to Chat Button */}
      <div className="mb-6">
        <Button 
          onClick={onBackToChat}
          variant="outline"
          className="border-indigo-400 text-indigo-700 hover:bg-indigo-50"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to CV Review
        </Button>
      </div>

      <div className="mb-8">
        <h2 className="text-2xl font-bold text-indigo-900 mb-2">Perfect Job Matches</h2>
        <p className="text-indigo-700">
          Found {jobMatches.length} {primaryCategory ? `${primaryCategory} ` : ''}jobs matching your profile
        </p>
      </div>

      {/* Application Limit Bar */}
      <div className="bg-gradient-to-r from-blue-100 to-indigo-100 p-4 rounded-xl shadow-lg border-2 border-blue-200 mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-indigo-900">Job Applications</h3>
            <p className="text-sm text-indigo-700">
              You can apply to up to {MAX_APPLICATIONS} jobs. {remainingApplications} applications remaining.
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="flex gap-1">
              {Array.from({ length: MAX_APPLICATIONS }).map((_, index) => (
                <div
                  key={index}
                  className={`w-4 h-4 rounded-full ${
                    index < selectedJobs.length ? 'bg-indigo-600' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
            <span className="text-lg font-bold text-indigo-900">
              {selectedJobs.length}/{MAX_APPLICATIONS}
            </span>
          </div>
        </div>
      </div>

      <div className="grid gap-6 mb-8">
        {jobMatches.map((job) => (
          <div key={job.id} className="bg-gradient-to-br from-amber-50 to-yellow-100 p-6 rounded-xl shadow-lg border-2 border-amber-200 hover:shadow-xl transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start gap-4 flex-1">
                <button
                  onClick={() => handleJobSelect(job.id)}
                  disabled={!selectedJobs.includes(job.id) && selectedJobs.length >= MAX_APPLICATIONS}
                  className={`mt-1 ${
                    !selectedJobs.includes(job.id) && selectedJobs.length >= MAX_APPLICATIONS
                      ? 'opacity-50 cursor-not-allowed' 
                      : 'hover:scale-110 transition-transform'
                  }`}
                >
                  {selectedJobs.includes(job.id) ? (
                    <CheckSquare className="w-6 h-6 text-indigo-600" />
                  ) : (
                    <Square className="w-6 h-6 text-gray-400" />
                  )}
                </button>
                
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-xl font-semibold text-indigo-900">{job.title}</h3>
                    <div className="flex items-center gap-1 bg-green-100 px-2 py-1 rounded-full">
                      <Star className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-green-600">{job.matchPercentage}% match</span>
                    </div>
                  </div>
                  <p className="text-lg text-blue-600 font-medium mb-2">{job.company}</p>
                  <div className="flex items-center gap-4 text-indigo-600 text-sm mb-3">
                    <div className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      <span>{job.location}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Briefcase className="w-4 h-4" />
                      <span>{job.salary}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{job.type}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <p className="text-indigo-800 mb-4">{job.description}</p>

            <div className="mb-4">
              <h4 className="font-medium text-indigo-900 mb-2">Requirements:</h4>
              <div className="flex flex-wrap gap-2">
                {job.requirements.map((req, index) => (
                  <div key={index} className="bg-amber-200 text-amber-900 px-2 py-1 rounded text-xs font-medium">
                    {req}
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-end">
              <Button
                onClick={() => handleJobDetails(job.id)}
                variant="outline"
                className="border-indigo-400 text-indigo-700 hover:bg-indigo-50"
              >
                <Eye className="w-4 h-4 mr-2" />
                View Details
              </Button>
            </div>
          </div>
        ))}
      </div>

      {selectedJobs.length > 0 && (
        <div className="sticky bottom-4">
          <div className="bg-gradient-to-r from-amber-100 to-yellow-100 p-4 rounded-xl shadow-xl border-2 border-amber-300">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-indigo-900">
                  {selectedJobs.length} job{selectedJobs.length > 1 ? 's' : ''} selected
                </p>
                <p className="text-sm text-indigo-700">
                  AI will create personalized applications for each position
                </p>
              </div>
              <Button 
                onClick={handleApplyToJobs}
                className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
              >
                <Send className="w-4 h-4 mr-2" />
                Apply to Jobs
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default JobMatches;
