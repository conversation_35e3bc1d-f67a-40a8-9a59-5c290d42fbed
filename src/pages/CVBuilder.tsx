
import { useState } from 'react';
import ChatInterface from '@/components/ChatInterface';
import CVPreview from '@/components/CVPreview';
import Header from '@/components/Header';
import TemplateSelector from '@/components/TemplateSelector';
import { UserProfile } from '@/types/UserProfile';
import { useLanguage } from '@/contexts/LanguageContext';

const CVBuilder = () => {
  const { t } = useLanguage();
  const [userProfile, setUserProfile] = useState<UserProfile>({
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      address: '',
      nationality: ''
    },
    workExperience: [],
    skills: [],
    languages: [],
    education: [],
    certifications: []
  });

  const [selectedTemplate, setSelectedTemplate] = useState('');

  const handleTemplateSelect = (template: string) => {
    setSelectedTemplate(template);
  };

  const handleProfileUpdate = (profile: UserProfile) => {
    setUserProfile(profile);
  };

  const handleChatCompletion = () => {
    // CV completion handled by the CV preview component
  };

  // Check if profile is complete for enabling buttons
  const isProfileComplete = (profile: UserProfile): boolean => {
    return (
      profile.personalInfo.fullName.length > 0 &&
      profile.personalInfo.email.length > 0 &&
      profile.personalInfo.phone.length > 0 &&
      profile.personalInfo.address.length > 0 &&
      profile.personalInfo.nationality.length > 0 &&
      profile.workExperience.length > 0 &&
      profile.skills.length > 0 &&
      profile.languages.length > 0
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t('cvbuilder.title')}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('cvbuilder.description')}
          </p>
        </div>

        {!selectedTemplate ? (
          <TemplateSelector onTemplateSelect={handleTemplateSelect} />
        ) : (
          <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <div className="order-2 lg:order-1">
              <ChatInterface 
                onProfileUpdate={handleProfileUpdate}
                onChatCompletion={handleChatCompletion}
                onTemplateSelect={handleTemplateSelect}
              />
            </div>
            
            <div className="order-1 lg:order-2">
              <CVPreview
                userProfile={userProfile}
                template={selectedTemplate}
                isEnabled={isProfileComplete(userProfile)}
                onCVComplete={handleChatCompletion}
              />
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default CVBuilder;
