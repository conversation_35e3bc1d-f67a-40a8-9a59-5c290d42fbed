import { UserProfile } from '@/types/UserProfile';

export interface CVTemplate {
  id: string;
  name: string;
  description: string;
  suitableFor: string[];
  color: string;
  style: 'modern' | 'classic' | 'creative' | 'executive';
}

export const CV_TEMPLATES: CVTemplate[] = [
  {
    id: 'hospitality-modern',
    name: 'Hospitality Professional',
    description: 'Modern, clean design perfect for hotel, restaurant, and bar professionals',
    suitableFor: ['waiter', 'server', 'bartender', 'hotel receptionist', 'restaurant manager'],
    color: 'blue',
    style: 'modern'
  },
  {
    id: 'culinary-creative',
    name: 'Culinary Artist',
    description: 'Creative design showcasing culinary skills and kitchen experience',
    suitableFor: ['chef', 'cook', 'kitchen assistant', 'pastry chef', 'sous chef'],
    color: 'orange',
    style: 'creative'
  },
  {
    id: 'service-classic',
    name: 'Service Excellence',
    description: 'Traditional German format trusted by employers for service roles',
    suitableFor: ['housekeeper', 'cleaner', 'maintenance', 'security', 'concierge'],
    color: 'green',
    style: 'classic'
  },
  {
    id: 'management-executive',
    name: 'Leadership Executive',
    description: 'Professional executive design for management and supervisory positions',
    suitableFor: ['manager', 'supervisor', 'team leader', 'department head', 'assistant manager'],
    color: 'purple',
    style: 'executive'
  }
];

export const getTemplateForRole = (jobRole: string): CVTemplate => {
  const normalizedRole = jobRole.toLowerCase();
  
  // Find the best matching template based on job role
  const matchingTemplate = CV_TEMPLATES.find(template => 
    template.suitableFor.some(role => 
      normalizedRole.includes(role) || role.includes(normalizedRole)
    )
  );
  
  // Default to hospitality-modern if no specific match
  return matchingTemplate || CV_TEMPLATES[0];
};

export const renderCVTemplate = (userProfile: UserProfile, template: CVTemplate): string => {
  switch (template.id) {
    case 'hospitality-modern':
      return renderHospitalityModern(userProfile);
    case 'culinary-creative':
      return renderCulinaryCreative(userProfile);
    case 'service-classic':
      return renderServiceClassic(userProfile);
    case 'management-executive':
      return renderManagementExecutive(userProfile);
    default:
      return renderHospitalityModern(userProfile);
  }
};

const renderHospitalityModern = (profile: UserProfile): string => {
  return `
    <div class="bg-white min-h-[400px] font-sans">
      <!-- Header with Blue Gradient -->
      <div class="bg-gradient-to-r from-blue-600 via-indigo-600 to-blue-800 text-white p-8 relative overflow-hidden">
        <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
        <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
        <div class="relative z-10">
          <h1 class="text-3xl font-bold mb-2">${profile.personalInfo.fullName}</h1>
          <p class="text-blue-100 text-lg font-medium mb-4">Hospitality Professional</p>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-white rounded-full"></div>
                <span>${profile.personalInfo.email}</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-white rounded-full"></div>
                <span>${profile.personalInfo.phone}</span>
              </div>
            </div>
            <div class="space-y-2">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-white rounded-full"></div>
                <span>${profile.personalInfo.address}</span>
              </div>
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-white rounded-full"></div>
                <span>${profile.personalInfo.nationality}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Content sections would go here -->
      <div class="p-8 space-y-8">
        <!-- Work Experience, Skills, etc. -->
      </div>
    </div>
  `;
};

const renderCulinaryCreative = (profile: UserProfile): string => {
  return `
    <div class="bg-white min-h-[400px] font-sans">
      <!-- Creative Header with Orange Theme -->
      <div class="bg-gradient-to-r from-orange-500 via-red-500 to-orange-600 text-white p-8 relative">
        <div class="absolute top-4 right-4 text-6xl opacity-20">👨‍🍳</div>
        <h1 class="text-3xl font-bold mb-2">${profile.personalInfo.fullName}</h1>
        <p class="text-orange-100 text-lg font-medium mb-4">Culinary Professional</p>
        <div class="text-sm space-y-1">
          <p>${profile.personalInfo.email} | ${profile.personalInfo.phone}</p>
          <p>${profile.personalInfo.address}</p>
          <p>Nationality: ${profile.personalInfo.nationality}</p>
        </div>
      </div>
      
      <div class="p-8 space-y-8">
        <!-- Content sections -->
      </div>
    </div>
  `;
};

const renderServiceClassic = (profile: UserProfile): string => {
  return `
    <div class="bg-white min-h-[400px] font-serif p-8">
      <!-- Classic Header -->
      <div class="border-b-4 border-green-600 pb-6 mb-8">
        <div class="text-center">
          <h1 class="text-3xl font-bold text-gray-800 mb-2">${profile.personalInfo.fullName}</h1>
          <p class="text-green-600 text-lg font-medium mb-4">Service Professional</p>
          <div class="flex justify-center space-x-8 text-sm text-gray-600">
            <span>${profile.personalInfo.email}</span>
            <span>${profile.personalInfo.phone}</span>
            <span>${profile.personalInfo.nationality}</span>
          </div>
          <p class="text-sm text-gray-600 mt-2">${profile.personalInfo.address}</p>
        </div>
      </div>
      
      <div class="space-y-8">
        <!-- Content sections -->
      </div>
    </div>
  `;
};

const renderManagementExecutive = (profile: UserProfile): string => {
  return `
    <div class="bg-white min-h-[400px] font-sans flex">
      <!-- Executive Sidebar -->
      <div class="w-1/3 bg-gradient-to-b from-purple-800 to-purple-900 text-white p-6">
        <div class="text-center mb-8">
          <div class="w-24 h-24 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full mx-auto mb-4 flex items-center justify-center">
            <span class="text-2xl font-bold">${profile.personalInfo.fullName?.charAt(0) || 'U'}</span>
          </div>
          <h1 class="text-xl font-bold mb-2">${profile.personalInfo.fullName}</h1>
          <p class="text-purple-300 text-sm">Management Professional</p>
        </div>
        
        <!-- Contact Info -->
        <div class="space-y-3 text-sm">
          <div>
            <p class="text-purple-300">Email</p>
            <p class="text-white text-xs">${profile.personalInfo.email}</p>
          </div>
          <div>
            <p class="text-purple-300">Phone</p>
            <p class="text-white text-xs">${profile.personalInfo.phone}</p>
          </div>
          <div>
            <p class="text-purple-300">Address</p>
            <p class="text-white text-xs">${profile.personalInfo.address}</p>
          </div>
          <div>
            <p class="text-purple-300">Nationality</p>
            <p class="text-white text-xs">${profile.personalInfo.nationality}</p>
          </div>
        </div>
      </div>
      
      <!-- Main Content -->
      <div class="w-2/3 p-8">
        <!-- Content sections -->
      </div>
    </div>
  `;
};
