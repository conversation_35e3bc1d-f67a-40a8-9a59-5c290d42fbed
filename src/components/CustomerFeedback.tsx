import React from 'react';
import { Card } from '@/components/ui/card';
import { Star, Quote } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
const CustomerFeedback = () => {
  const {
    t,
    language
  } = useLanguage();
  const testimonials = language === 'de' ? [{
    name: "<PERSON>",
    role: "Elektriker",
    location: "München",
    rating: 5,
    text: "Fantastisch! Ich hatte nie gedacht, dass es so einfach sein könnte, einen professionellen Lebenslauf zu erstellen. Die KI hat alle meine Fähigkeiten perfekt erfasst.",
    avatar: "<PERSON>"
  }, {
    name: "<PERSON>",
    role: "Krankenpflegerin",
    location: "Berlin",
    rating: 5,
    text: "Dank sohnus habe ich schnell einen neuen Job gefunden. Der Lebenslauf sah so professionell aus und hat alle wichtigen Punkte hervorgehoben.",
    avatar: "A<PERSON>"
  }, {
    name: "<PERSON>",
    role: "<PERSON><PERSON><PERSON><PERSON>",
    location: "Hamburg",
    rating: 5,
    text: "Sehr benutzerfreundlich und schnell. Innerhalb von 10 Minuten hatte ich einen fertigen <PERSON>, der besser aussah als alles, was ich vorher gemacht hatte.",
    avatar: "TM"
  }] : [{
    name: "<PERSON> <PERSON>",
    role: "Electrician",
    location: "Munich",
    rating: 5,
    text: "Fantastic! I never thought it could be so easy to create a professional CV. The AI captured all my skills perfectly.",
    avatar: "MS"
  }, {
    name: "Anna <PERSON>",
    role: "Nurse",
    location: "Berlin",
    rating: 5,
    text: "Thanks to sohnus I quickly found a new job. The CV looked so professional and highlighted all the important points.",
    avatar: "AW"
  }, {
    name: "Thomas Müller",
    role: "Mechanic",
    location: "Hamburg",
    rating: 5,
    text: "Very user-friendly and fast. Within 10 minutes I had a finished CV that looked better than anything I had done before.",
    avatar: "TM"
  }];
  return <div className="py-16 px-4 bg-gradient-to-br from-amber-50 via-yellow-25 to-indigo-50">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-indigo-900 mb-4">
            What Our Users Say
          </h2>
          <p className="text-lg text-indigo-700 max-w-2xl mx-auto">Real success stories who found their dream jobs through our platform</p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => <Card key={index} className="p-6 bg-white/90 backdrop-blur-sm border-2 border-amber-100 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 relative">
              <Quote className="absolute top-4 right-4 w-8 h-8 text-amber-200" />
              
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full flex items-center justify-center text-indigo-900 font-semibold mr-4 shadow-md">
                  {testimonial.avatar}
                </div>
                <div>
                  <h4 className="font-semibold text-indigo-900">{testimonial.name}</h4>
                  <p className="text-sm text-indigo-700">{testimonial.role}</p>
                  <p className="text-sm text-amber-600">{testimonial.location}</p>
                </div>
              </div>
              
              <div className="flex mb-4">
                {[...Array(testimonial.rating)].map((_, i) => <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />)}
              </div>
              
              <p className="text-gray-700 leading-relaxed italic">
                "{testimonial.text}"
              </p>
            </Card>)}
        </div>
      </div>
    </div>;
};
export default CustomerFeedback;