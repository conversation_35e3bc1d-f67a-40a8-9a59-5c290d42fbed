
import React from 'react';
import Header from '@/components/Header';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import HowItWorks from '@/components/HowItWorks';
import CustomerFeedback from '@/components/CustomerFeedback';
import Footer from '@/components/Footer';

const Index = () => {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-amber-25 to-indigo-100">
      <Header />
      
      <main className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto mb-16">
          <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
            {t('hero.title')} 
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-amber-500 to-indigo-600 block">
              {t('hero.title.highlight')}
            </span>
          </h1>
          
          <h2 className="text-2xl lg:text-3xl font-semibold text-indigo-800 mb-4">
            {t('hero.subtitle')}
          </h2>
          
          <p className="text-lg text-amber-600 font-medium mb-8">
            {t('hero.description.tagline')}
          </p>
          
          <p className="text-xl text-gray-600 leading-relaxed mb-12 max-w-3xl mx-auto">
            {t('hero.description')}
          </p>

          <Link to="/job-matching">
            <Button size="lg" className="bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600 text-white px-12 py-4 text-xl font-medium mb-16 shadow-lg hover:shadow-xl transition-all duration-300">
              {t('hero.cta.primary')}
              <ArrowRight className="w-6 h-6 ml-3" />
            </Button>
          </Link>
        </div>

        <HowItWorks />
      </main>

      <CustomerFeedback />
      <Footer />
    </div>
  );
};

export default Index;
