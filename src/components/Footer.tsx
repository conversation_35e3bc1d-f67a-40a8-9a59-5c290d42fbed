import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { User, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
const Footer = () => {
  const {
    t
  } = useLanguage();
  return <footer className="bg-gradient-to-br from-indigo-900 via-purple-900 to-indigo-800 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <Link to="/" className="flex items-center gap-3">
              <div className="bg-gradient-to-r from-amber-400 to-yellow-500 p-2 rounded-lg shadow-lg">
                <User className="w-6 h-6 text-indigo-900" />
              </div>
              <div>
                <h3 className="text-xl font-bold">sohnus</h3>
                <p className="text-sm text-amber-200">Your true potential, instantly seen. Get hired faster.</p>
              </div>
            </Link>
            <p className="text-gray-300 text-sm leading-relaxed">
              Connecting hospitality professionals with their perfect jobs in Germany through AI-powered matching and personalized CV creation.
            </p>
            <div className="flex space-x-3 pt-2">
              <a href="#" className="text-amber-200 hover:text-yellow-300 transition-colors">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="text-amber-200 hover:text-yellow-300 transition-colors">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-amber-200 hover:text-yellow-300 transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
              <a href="#" className="text-amber-200 hover:text-yellow-300 transition-colors">
                <Linkedin className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Services */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-amber-200">Our Services</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/job-matching" className="text-gray-300 hover:text-white transition-colors hover:underline">
                  AI Job Matching
                </Link>
              </li>
              <li>
                <Link to="/cv-builder" className="text-gray-300 hover:text-white transition-colors hover:underline">
                  Professional CV Builder
                </Link>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors hover:underline">
                  Career Coaching
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors hover:underline">
                  Interview Preparation
                </a>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-amber-200">Resources</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors hover:underline">
                  Job Market Guide
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors hover:underline">
                  German Work Visa Info
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors hover:underline">
                  Language Learning
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-300 hover:text-white transition-colors hover:underline">
                  Success Stories
                </a>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="text-lg font-semibold mb-4 text-amber-200">Get in Touch</h4>
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-2 text-gray-300">
                <Mail className="w-4 h-4 text-amber-200" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-2 text-gray-300">
                <Phone className="w-4 h-4 text-amber-200" />
                <span>+49 30 555 0123</span>
              </div>
              <div className="flex items-center gap-2 text-gray-300">
                <MapPin className="w-4 h-4 text-amber-200" />
                <span>Alexanderplatz 1, 10178 Berlin</span>
              </div>
            </div>
            <div className="mt-4 p-3 bg-indigo-800/50 rounded-lg">
              <p className="text-xs text-gray-300">
                <strong className="text-amber-200">Business Hours:</strong><br />
                Mon-Fri: 9:00 AM - 6:00 PM CET<br />
                Sat: 10:00 AM - 2:00 PM CET
              </p>
            </div>
          </div>
        </div>

        <div className="border-t border-indigo-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center text-sm text-gray-300">
            <p>&copy; 2024 sohnus GmbH. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="hover:text-amber-200 transition-colors">Privacy Policy</a>
              <a href="#" className="hover:text-amber-200 transition-colors">Terms of Service</a>
              <a href="#" className="hover:text-amber-200 transition-colors">Cookie Policy</a>
              <a href="#" className="hover:text-amber-200 transition-colors">Imprint</a>
            </div>
          </div>
        </div>
      </div>
    </footer>;
};
export default Footer;