
import React, { useState, useEffect } from 'react';
import { ArrowLeft, MapPin, Briefcase, Clock, Star, Send, Paperclip } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import Header from '@/components/Header';
import { useNavigate } from 'react-router-dom';

interface JobMatch {
  id: string;
  title: string;
  company: string;
  location: string;
  salary: string;
  type: string;
  matchPercentage: number;
  description: string;
  requirements: string[];
  category: string;
}

interface UserProfile {
  personalInfo: {
    fullName: string;
    email: string;
    phone: string;
    address: string;
    nationality: string;
  };
}

const JobDetails = () => {
  const navigate = useNavigate();
  const [job, setJob] = useState<JobMatch | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [applicationLetter, setApplicationLetter] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  useEffect(() => {
    const jobData = localStorage.getItem('selectedJob');
    const profileData = localStorage.getItem('userProfile');
    
    if (jobData) {
      const parsedJob = JSON.parse(jobData);
      setJob(parsedJob);
      
      // Generate initial application letter
      const initialLetter = `Dear Hiring Manager,

I am writing to express my strong interest in the ${parsedJob.title} position at ${parsedJob.company}. With my background in hospitality and relevant experience, I believe I would be a valuable addition to your team.

${parsedJob.category === 'bartender' ? 
  'My experience in bartending and cocktail preparation, combined with my customer service skills, makes me well-suited for this role. I am passionate about creating exceptional experiences for guests and maintaining high standards of service.' :
  parsedJob.category === 'server' ?
  'My experience in food service and customer relations has prepared me well for this server position. I excel at multitasking in fast-paced environments while maintaining attention to detail and providing excellent customer service.' :
  'My hospitality experience has equipped me with the skills necessary to excel in this role. I am dedicated to providing exceptional service and contributing to a positive team environment.'
}

I am particularly drawn to this opportunity because of your company's reputation and the chance to work in ${parsedJob.location}. I am available for ${parsedJob.type.toLowerCase()} work and am excited about the possibility of joining your team.

Thank you for considering my application. I look forward to hearing from you soon.

Best regards,
${profileData ? JSON.parse(profileData).personalInfo.fullName : '[Your Name]'}`;
      
      setApplicationLetter(initialLetter);
    }
    
    if (profileData) {
      setUserProfile(JSON.parse(profileData));
    }
  }, []);

  const handleSubmitApplication = () => {
    setIsSubmitted(true);
  };

  const handleBack = () => {
    navigate('/job-matching');
  };

  if (!job) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-amber-25 to-orange-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Job not found</h1>
            <Button onClick={() => navigate('/')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-amber-25 to-orange-50">
        <Header />
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            <div className="bg-gradient-to-br from-green-100 to-emerald-100 p-8 rounded-xl shadow-lg border-2 border-green-200">
              <div className="text-center">
                <div className="bg-green-200 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Send className="w-10 h-10 text-green-700" />
                </div>
                <h2 className="text-3xl font-bold text-green-800 mb-4">Application Sent!</h2>
                <p className="text-lg text-green-700 mb-6">
                  Your application for {job.title} at {job.company} has been submitted successfully.
                </p>
                <p className="text-green-600 mb-8">
                  The employer will review your application and respond within 2-3 business days.
                </p>
                <Button 
                  onClick={handleBack}
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white mr-4"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Jobs
                </Button>
                <Button 
                  onClick={() => navigate('/')}
                  variant="outline"
                >
                  Home
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-amber-25 to-orange-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Button 
            onClick={handleBack}
            variant="outline" 
            className="mb-6"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Jobs
          </Button>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Job Details */}
            <Card className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <h1 className="text-2xl font-bold text-indigo-900">{job.title}</h1>
                <div className="flex items-center gap-1 bg-green-100 px-3 py-1 rounded-full">
                  <Star className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-600">{job.matchPercentage}% match</span>
                </div>
              </div>

              <p className="text-xl text-blue-600 font-medium mb-4">{job.company}</p>

              <div className="flex flex-wrap gap-4 text-indigo-600 text-sm mb-6">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>{job.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Briefcase className="w-4 h-4" />
                  <span>{job.salary}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>{job.type}</span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold text-indigo-900 mb-3">Job Description</h3>
                <p className="text-gray-700 leading-relaxed">{job.description}</p>
              </div>

              <div className="mb-6">
                <h3 className="text-lg font-semibold text-indigo-900 mb-3">Requirements</h3>
                <div className="flex flex-wrap gap-2">
                  {job.requirements.map((req, index) => (
                    <span key={index} className="bg-amber-200 text-amber-900 px-3 py-1 rounded-full text-sm font-medium">
                      {req}
                    </span>
                  ))}
                </div>
              </div>
            </Card>

            {/* Application Letter */}
            <Card className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <h3 className="text-lg font-semibold text-indigo-900">Application Letter</h3>
                <div className="flex items-center gap-1 bg-blue-100 px-2 py-1 rounded-full">
                  <Paperclip className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-600">CV Attached</span>
                </div>
              </div>
              <textarea
                value={applicationLetter}
                onChange={(e) => setApplicationLetter(e.target.value)}
                className="w-full h-64 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                placeholder="Write your application letter here..."
              />
              
              <div className="mt-6 text-center">
                <Button
                  onClick={handleSubmitApplication}
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-3"
                  disabled={!applicationLetter.trim()}
                >
                  <Send className="w-4 h-4 mr-2" />
                  Submit Application
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
};

export default JobDetails;
