// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jcvogzacfhckhwaenowj.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Impjdm9nemFjZmhja2h3YWVub3dqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE0NzQ2NjAsImV4cCI6MjA2NzA1MDY2MH0.vv-PwsKEPX8XlGOT8v-usPCD9Ozuj-N6Ba_oJ3-SChc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});