
import React, { useState } from 'react';
import ChatInterface from '@/components/ChatInterface';
import CVPreview from '@/components/CVPreview';
import JobMatches from '@/components/JobMatches';
import Header from '@/components/Header';
import { UserProfile } from '@/types/UserProfile';
import { useLanguage } from '@/contexts/LanguageContext';

const JobMatching = () => {
  const { t } = useLanguage();
  const [userProfile, setUserProfile] = useState<UserProfile>({
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      address: '',
      nationality: ''
    },
    workExperience: [],
    skills: [],
    languages: [],
    education: [],
    certifications: []
  });

  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [userJobRole, setUserJobRole] = useState('');
  const [isChatCompleted, setIsChatCompleted] = useState(false);
  const [showJobMatches, setShowJobMatches] = useState(false);

  const handleTemplateSelect = (template: string) => {
    setSelectedTemplate(template);
  };

  const handleProfileUpdate = (profile: UserProfile) => {
    setUserProfile(profile);
  };

  const handleChatCompletion = () => {
    setIsChatCompleted(true);
  };

  const handleCVComplete = () => {
    setShowJobMatches(true);
  };

  const handleBackToChat = () => {
    setShowJobMatches(false);
  };

  // Check if profile is complete for enabling buttons
  const isProfileComplete = (profile: UserProfile): boolean => {
    return (
      profile.personalInfo.fullName.length > 0 &&
      profile.personalInfo.email.length > 0 &&
      profile.personalInfo.phone.length > 0 &&
      profile.personalInfo.address.length > 0 &&
      profile.personalInfo.nationality.length > 0 &&
      profile.workExperience.length > 0 &&
      profile.skills.length > 0 &&
      profile.languages.length > 0
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 via-amber-25 to-orange-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {!showJobMatches ? (
          <>
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-indigo-900 mb-4">
                {t('jobmatching.title')}
              </h1>
              <p className="text-lg text-indigo-700 max-w-2xl mx-auto">
                {t('jobmatching.description')}
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
              <div className="order-2 lg:order-1">
                <ChatInterface 
                  onProfileUpdate={handleProfileUpdate}
                  onChatCompletion={handleChatCompletion}
                  onTemplateSelect={handleTemplateSelect}
                />
              </div>
              
              <div className="order-1 lg:order-2">
                <CVPreview 
                  userProfile={userProfile}
                  template={selectedTemplate}
                  isEnabled={isProfileComplete(userProfile)}
                  onCVComplete={handleCVComplete}
                />
              </div>
            </div>
          </>
        ) : (
          <JobMatches 
            userProfile={userProfile}
            selectedJobType="hospitality"
            userJobRole={userJobRole}
            onBackToChat={handleBackToChat}
          />
        )}
      </main>
    </div>
  );
};

export default JobMatching;
