import { UserProfile } from '@/types/UserProfile';
import { extractUserProfile, mergeProfileData, getNextConversationState } from './profileService';

/**
 * SINGLE SOURCE OF TRUTH for conversation logic
 * This service handles all conversation flow and response generation
 */

export interface ConversationResponse {
  message: string;
  userProfile?: Partial<UserProfile>;
  nextState: string;
}

export const generateLocalResponse = (
  userInput: string, 
  conversationState: string, 
  currentProfile: UserProfile
): ConversationResponse => {
  console.log('=== Local Conversation Processing ===');
  console.log('userInput:', userInput);
  console.log('conversationState:', conversationState);

  // Extract profile data from user input
  const extractedProfile = extractUserProfile(userInput, conversationState, currentProfile);
  
  // Determine next state
  const nextState = getNextConversationState(conversationState, extractedProfile, currentProfile);
  
  // Generate appropriate response message
  let message = '';

  switch (conversationState) {
    case 'personal_info':
      if (!currentProfile.personalInfo.fullName && extractedProfile.personalInfo?.fullName) {
        message = `Perfect! Hello ${extractedProfile.personalInfo.fullName}! 👋\n\nWhat's your email address?\n\n💡 Employers will use this to contact you`;
      } else if (!currentProfile.personalInfo.email && extractedProfile.personalInfo?.email) {
        message = `Great! 📧 ${extractedProfile.personalInfo.email}\n\nWhat's your phone number?\n\n💡 Employers will call you for interviews`;
      } else if (!currentProfile.personalInfo.phone && extractedProfile.personalInfo?.phone) {
        message = `Perfect! 📱\n\nWhat's your address in Germany?\n\n💡 Example: Hauptstraße 123, 10115 Berlin`;
      } else if (!currentProfile.personalInfo.fullName) {
        message = `What's your full name?\n\n💡 Please include both first and last name`;
      } else if (!currentProfile.personalInfo.email) {
        message = `What's your email address?\n\n💡 Employers will use this to contact you`;
      } else if (!currentProfile.personalInfo.phone) {
        message = `What's your phone number?\n\n💡 Employers will call you for interviews`;
      }
      break;

    case 'contact_details':
      if (!currentProfile.personalInfo.address && extractedProfile.personalInfo?.address) {
        message = `Great! 🏠\n\nWhat's your nationality?\n\n💡 Examples: German, Chinese, Turkish, Polish`;
      } else if (!currentProfile.personalInfo.nationality && extractedProfile.personalInfo?.nationality) {
        message = `Perfect! Now let's get your work experience.\n\nWhat was your job title at your most recent hospitality job?\n\n💡 Examples: Bartender, Waiter, Cleaner, Kitchen Helper`;
      } else if (!currentProfile.personalInfo.address) {
        message = `What's your address in Germany?\n\n💡 Example: Hauptstraße 123, 10115 Berlin`;
      } else if (!currentProfile.personalInfo.nationality) {
        message = `What's your nationality?\n\n💡 Examples: German, Chinese, Turkish, Polish`;
      }
      break;

    case 'work_experience':
      if (extractedProfile.workExperience && extractedProfile.workExperience.length > 0) {
        message = `Excellent! Now let's get your skills.\n\nWhat hospitality skills do you have?\n\n💡 Examples: Customer service, Cleaning, Teamwork, Food prep`;
      } else {
        message = `Tell me about your work experience in hospitality.\n\n💡 Examples: Bartender at Hotel Berlin, Waiter at Restaurant Munich`;
      }
      break;

    case 'skills':
      if (extractedProfile.skills && extractedProfile.skills.length > 0) {
        message = `Great! Now let's get your languages.\n\nWhat languages do you speak and how well?\n\n💡 Examples: German - Advanced, English - Basic`;
      } else {
        message = `What hospitality skills do you have?\n\n💡 Examples: Customer service, Cleaning, Teamwork, Food prep`;
      }
      break;

    case 'languages':
      if (extractedProfile.languages && extractedProfile.languages.length > 0) {
        message = `Perfect! Now let's get your education.\n\nWhat's your highest education level?\n\n💡 Examples: High School, University, Hotel Management Course`;
      } else {
        message = `What languages do you speak and how well?\n\n💡 Examples: German - Advanced, English - Basic`;
      }
      break;

    case 'education':
      if (extractedProfile.education && extractedProfile.education.length > 0) {
        message = `Excellent! Last step - certificates.\n\nDo you have any certificates for hospitality work?\n\n💡 Examples: Food Safety, First Aid, Language Certificate\n\n✨ Say "none" if you don't have any`;
      } else {
        message = `What's your highest education level?\n\n💡 Examples: High School, University, Hotel Management Course`;
      }
      break;

    case 'certifications':
      message = `🎉 Fantastic! Your CV is now complete and ready!\n\nYou can now download your professional CV or find matching jobs. Good luck with your job search!`;
      break;

    default:
      message = `I'm here to help you create your CV. Let's continue with the next step.`;
  }

  return {
    message,
    userProfile: extractedProfile,
    nextState
  };
};

export const getConversationPrompts = (conversationState: string, currentProfile: UserProfile): string => {
  const info = currentProfile.personalInfo;

  switch (conversationState) {
    case 'personal_info':
      if (!info.fullName) {
        return "Ask for their FULL NAME first. Keep it short and friendly. Include tip about using first and last name.";
      }
      if (!info.email) {
        return "Ask for their EMAIL ADDRESS next. Keep it short and mention it's for employers to contact them.";
      }
      if (!info.phone) {
        return "Ask for their PHONE NUMBER next. Keep it short and mention it's for direct contact.";
      }
      return "All personal info collected - move to address/nationality";

    case 'contact_details':
      if (!info.address) {
        return "Ask for their ADDRESS in Germany next. Keep it short and include example format.";
      }
      if (!info.nationality) {
        return "Ask for their NATIONALITY next. Keep it short and give examples.";
      }
      return "All contact details collected - move to work experience";

    case 'work_experience':
      return "Ask for ONE piece of work info at a time. Keep questions SHORT and bold important words. Give examples. Current priority: job title, then company, then duration, then responsibilities.";

    case 'skills':
      return "Ask for hospitality skills. Keep it SHORT, bold key words, give examples like: customer service, cleaning, teamwork.";

    case 'languages':
      return "Ask for languages and levels. Keep it SHORT, bold key words, give examples like: German - Advanced, English - Basic.";

    case 'education':
      return "Ask for education. Keep it SHORT, bold key words, give examples like: High School, Hotel Management Course.";

    case 'certifications':
      return "Ask for certificates. Keep it SHORT, bold key words, give examples like: Food Safety, First Aid.";

    default:
      return "Continue with the conversation naturally.";
  }
};

export const createProfileSummary = (profile: UserProfile): string => {
  const parts = [];
  
  if (profile.personalInfo.fullName) {
    parts.push(`Name: ${profile.personalInfo.fullName}`);
  }
  if (profile.personalInfo.email) {
    parts.push(`Email: ${profile.personalInfo.email}`);
  }
  if (profile.personalInfo.phone) {
    parts.push(`Phone: ${profile.personalInfo.phone}`);
  }
  if (profile.personalInfo.address) {
    parts.push(`Address: ${profile.personalInfo.address}`);
  }
  if (profile.personalInfo.nationality) {
    parts.push(`Nationality: ${profile.personalInfo.nationality}`);
  }
  if (profile.workExperience.length > 0) {
    parts.push(`Work: ${profile.workExperience.map(w => w.jobTitle).join(', ')}`);
  }
  if (profile.skills.length > 0) {
    parts.push(`Skills: ${profile.skills.map(s => s.name).join(', ')}`);
  }
  if (profile.languages.length > 0) {
    parts.push(`Languages: ${profile.languages.map(l => `${l.name} (${l.level})`).join(', ')}`);
  }
  if (profile.education.length > 0) {
    parts.push(`Education: ${profile.education.map(e => e.degree).join(', ')}`);
  }
  if (profile.certifications.length > 0) {
    parts.push(`Certifications: ${profile.certifications.map(c => c.name).join(', ')}`);
  }

  return parts.length > 0 ? parts.join('; ') : 'No information collected yet';
};

export const createConversationContext = (conversationState: string, currentProfile: UserProfile): string => {
  const missing = [];
  
  if (!currentProfile.personalInfo.fullName) missing.push('full name');
  if (!currentProfile.personalInfo.email) missing.push('email');
  if (!currentProfile.personalInfo.phone) missing.push('phone');
  if (!currentProfile.personalInfo.address) missing.push('address');
  if (!currentProfile.personalInfo.nationality) missing.push('nationality');
  if (currentProfile.workExperience.length === 0) missing.push('work experience');
  if (currentProfile.skills.length === 0) missing.push('skills');
  if (currentProfile.languages.length === 0) missing.push('languages');
  if (currentProfile.education.length === 0) missing.push('education');
  if (currentProfile.certifications.length === 0) missing.push('certifications');

  return missing.length > 0 ? `Still need: ${missing.join(', ')}` : 'Profile is complete';
};
