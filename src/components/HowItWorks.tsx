
import React from 'react';
import { Card } from '@/components/ui/card';
import { MessageCircle, FileText, Briefcase, Send, ArrowRight } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const HowItWorks = () => {
  const { t } = useLanguage();

  const steps = [
    {
      id: 1,
      title: "Tell Us About Yourself",
      description: "Share your hospitality experience, skills, and career goals through our AI chat assistant.",
      icon: <MessageCircle className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-amber-50 to-yellow-100",
      textColor: "text-amber-600"
    },
    {
      id: 2,
      title: "AI Creates Your CV",
      description: "Our AI builds a professional, hospitality-focused CV tailored to German employers.",
      icon: <FileText className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-yellow-50 to-amber-100",
      textColor: "text-yellow-600"
    },
    {
      id: 3,
      title: "Find Perfect Matches",
      description: "Get matched with hospitality jobs that fit your skills, location, and salary expectations.",
      icon: <Briefcase className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-indigo-50 to-purple-100",
      textColor: "text-indigo-600"
    },
    {
      id: 4,
      title: "Apply Instantly",
      description: "Submit applications with personalized cover letters to multiple employers with one click.",
      icon: <Send className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-purple-50 to-indigo-100",
      textColor: "text-purple-600"
    }
  ];

  return (
    <div className="relative max-w-7xl mx-auto mb-16">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 left-1/4 w-32 h-32 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div className="absolute bottom-10 right-1/4 w-24 h-24 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>
      
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 relative">
        {steps.map((step, index) => (
          <div key={step.id} className="relative group">
            <Card className="p-8 h-full bg-white/90 backdrop-blur-sm border-2 border-amber-100 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 hover:scale-105 hover:border-amber-200">
              <div className="text-center">
                <div className={`w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6 ${step.bgColor} group-hover:scale-110 transition-transform duration-300 shadow-lg border-2 border-white`}>
                  <div className={step.textColor}>
                    {step.icon}
                  </div>
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-4 leading-tight">
                  {step.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {step.description}
                </p>
              </div>
            </Card>
            
            {/* Elegant Gradient Arrow Connection */}
            {index < steps.length - 1 && (
              <div className="hidden lg:block absolute top-1/2 -right-8 z-20 transform -translate-y-1/2">
                <div className="flex items-center">
                  <div className="w-8 h-1 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full shadow-lg"></div>
                  <div className="w-3 h-3 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full transform rotate-45 -ml-1 shadow-lg"></div>
                </div>
              </div>
            )}
            
            {/* Mobile Arrow (vertical) */}
            {index < steps.length - 1 && (
              <div className="lg:hidden flex justify-center mt-6 mb-2">
                <div className="flex flex-col items-center">
                  <div className="h-8 w-1 bg-gradient-to-b from-amber-400 to-yellow-500 rounded-full shadow-lg"></div>
                  <div className="w-3 h-3 bg-gradient-to-b from-amber-400 to-yellow-500 rounded-full transform rotate-45 -mt-1 shadow-lg"></div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default HowItWorks;
