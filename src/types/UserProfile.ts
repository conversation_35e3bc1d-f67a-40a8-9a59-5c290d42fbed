
export interface PersonalInfo {
  fullName: string;
  email: string;
  phone: string;
  address: string;
  nationality: string;
}

export interface WorkExperience {
  jobTitle: string;
  company: string;
  duration: string;
  description: string;
}

export interface Skill {
  name: string;
  level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert';
}

export interface Language {
  name: string;
  level: 'Basic' | 'Conversational' | 'Fluent' | 'Native';
}

export interface Education {
  degree: string;
  institution: string;
  year: string;
}

export interface Certification {
  name: string;
  issuer: string;
  year: string;
}

export interface UserProfile {
  personalInfo: PersonalInfo;
  workExperience: WorkExperience[];
  skills: Skill[];
  languages: Language[];
  education: Education[];
  certifications: Certification[];
}
