
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Link } from 'react-router-dom';
import { ArrowRight, CheckCircle, Users, Globe, Zap } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const HeroSection = () => {
  const { t } = useLanguage();

  const features = [
    { icon: <Users className="w-5 h-5" />, text: t('hero.feature1') },
    { icon: <Globe className="w-5 h-5" />, text: t('hero.feature2') },
    { icon: <Zap className="w-5 h-5" />, text: t('hero.feature3') }
  ];

  return (
    <div className="relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50" />
      <div className="absolute top-0 left-0 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse" />
      <div className="absolute top-0 right-0 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse" style={{ animationDelay: '2s' }} />
      
      <div className="relative container mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                {t('hero.title')} 
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600"> {t('hero.title.highlight')}</span>
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed">
                {t('hero.description')}
              </p>
            </div>

            {/* Features */}
            <div className="space-y-3">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3 text-gray-700">
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <div className="flex items-center gap-2">
                    {feature.icon}
                    <span>{feature.text}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link to="/cv-builder">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-medium">
                  {t('hero.cta.primary')}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link to="/about">
                <Button variant="outline" size="lg" className="px-8 py-3 text-lg font-medium">
                  {t('hero.cta.secondary')}
                </Button>
              </Link>
            </div>
          </div>

          {/* Right side - Visual */}
          <div className="relative">
            <Card className="p-8 bg-white/80 backdrop-blur-sm border-0 shadow-2xl">
              <div className="space-y-6">
                <div className="text-center">
                  <div className="bg-gradient-to-r from-blue-600 to-indigo-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">{t('hero.ai.ready')}</h3>
                  <p className="text-gray-600">{t('hero.ai.description')}</p>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-blue-800">
                      "{t('hero.ai.message')}"
                    </p>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-sm text-gray-600 italic">
                      {t('hero.ai.placeholder')}
                    </p>
                  </div>
                </div>
                
                <Link to="/cv-builder" className="block">
                  <Button className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
                    {t('hero.cta.try')}
                  </Button>
                </Link>
              </div>
            </Card>
            
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-20 h-20 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl opacity-70" />
            <div className="absolute -bottom-4 -left-4 w-20 h-20 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
