
import React from 'react';
import { User } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/contexts/LanguageContext';
import LanguageToggle from '@/components/LanguageToggle';

const Header = () => {
  const { t } = useLanguage();
  
  return (
    <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <Link to="/" className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-amber-400 to-yellow-500 p-2 rounded-lg">
              <User className="w-6 h-6 text-indigo-900" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">sohnus</h1>
              <p className="text-sm text-gray-600">Your true potential, instantly seen</p>
            </div>
          </Link>
          
          <div className="flex items-center gap-3">
            <LanguageToggle />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
