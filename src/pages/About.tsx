
import React from 'react';
import Header from '@/components/Header';
import { Bot, Users, Globe, CheckCircle } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

const About = () => {
  const { t } = useLanguage();

  const features = [
    {
      icon: Bot,
      title: t('about.feature1.title'),
      description: t('about.feature1.description')
    },
    {
      icon: Users,
      title: t('about.feature2.title'),
      description: t('about.feature2.description')
    },
    {
      icon: Globe,
      title: t('about.feature3.title'),
      description: t('about.feature3.description')
    },
    {
      icon: CheckCircle,
      title: t('about.feature4.title'),
      description: t('about.feature4.description')
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header />
      
      <main className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-6">
              {t('about.title')}
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              {t('about.description')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <feature.icon className="w-12 h-12 text-blue-600 mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>

          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">{t('about.mission')}</h2>
            <p className="text-gray-600 leading-relaxed mb-4">
              {t('about.mission.text1')}
            </p>
            <p className="text-gray-600 leading-relaxed">
              {t('about.mission.text2')}
            </p>
          </div>
        </div>
      </main>
    </div>
  );
};

export default About;
