┌─────────────────────────────────────────────────────────────┐
│                    FRONTEND LAYER                           │
├─────────────────────────────────────────────────────────────┤
│ ChatInterface.tsx                                           │
│ ├─ UI Logic Only                                           │
│ ├─ Message Display                                         │
│ └─ Input Handling                                          │
├─────────────────────────────────────────────────────────────┤
│ CVPreview.tsx                                              │
│ ├─ Display Logic Only                                      │
│ └─ PDF Generation                                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   SERVICE LAYER                             │
├─────────────────────────────────────────────────────────────┤
│ chatService.ts (SINGLE SOURCE OF TRUTH)                    │
│ ├─ processUserInput()                                      │
│ ├─ extractUserProfile()                                    │
│ ├─ mergeProfileData()                                      │
│ ├─ getNextConversationState()                             │
│ ├─ isProfileComplete()                                     │
│ └─ validateUserInput()                                     │
├─────────────────────────────────────────────────────────────┤
│ profileService.ts                                          │
│ ├─ Profile validation                                      │
│ ├─ Data transformation                                     │
│ └─ Type safety                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   BACKEND LAYER                             │
├─────────────────────────────────────────────────────────────┤
│ supabase/functions/gemini-chat/index.ts                    │
│ ├─ AI API calls ONLY                                       │
│ ├─ Prompt generation                                       │
│ ├─ Response formatting                                     │
│ └─ Error handling                                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    SHARED TYPES                             │
├─────────────────────────────────────────────────────────────┤
│ types/UserProfile.ts                                       │
│ types/ConversationState.ts                                 │
│ types/ChatResponse.ts                                      │
└─────────────────────────────────────────────────────────────┘