
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Star, Sparkles, Palette, Crown, Briefcase } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface TemplateSelectorProps {
  onTemplateSelect: (template: string) => void;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({ onTemplateSelect }) => {
  const { t } = useLanguage();

  const templates = [
    {
      id: 'modern',
      name: 'Modern Elegance',
      description: 'Clean, contemporary design perfect for service roles',
      icon: <Sparkles className="w-8 h-8" />,
      color: 'from-blue-500 via-blue-600 to-indigo-600',
      bgGradient: 'from-blue-50 via-indigo-50 to-purple-50',
      borderColor: 'border-blue-200',
      popular: true,
      preview: (
        <div className="w-full h-32 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-lg relative overflow-hidden">
          <div className="absolute top-2 left-2 w-8 h-8 bg-white/20 rounded-full"></div>
          <div className="absolute top-4 left-12 space-y-1">
            <div className="w-16 h-2 bg-white/80 rounded"></div>
            <div className="w-12 h-1 bg-white/60 rounded"></div>
          </div>
          <div className="absolute bottom-2 left-2 right-2 space-y-1">
            <div className="w-full h-1 bg-white/40 rounded"></div>
            <div className="w-3/4 h-1 bg-white/40 rounded"></div>
            <div className="w-1/2 h-1 bg-white/40 rounded"></div>
          </div>
        </div>
      )
    },
    {
      id: 'classic',
      name: 'Professional Classic',
      description: 'Traditional format trusted by German employers',
      icon: <Crown className="w-8 h-8" />,
      color: 'from-emerald-500 via-green-600 to-teal-600',
      bgGradient: 'from-emerald-50 via-green-50 to-teal-50',
      borderColor: 'border-emerald-200',
      popular: false,
      preview: (
        <div className="w-full h-32 bg-white border-2 border-emerald-600 rounded-lg shadow-lg relative overflow-hidden">
          <div className="absolute top-2 left-2 right-2 h-6 bg-emerald-600 rounded"></div>
          <div className="absolute top-10 left-2 right-2 space-y-1">
            <div className="w-full h-1 bg-emerald-200 rounded"></div>
            <div className="w-4/5 h-1 bg-emerald-200 rounded"></div>
            <div className="w-3/5 h-1 bg-emerald-200 rounded"></div>
          </div>
          <div className="absolute bottom-2 left-2 right-2 space-y-1">
            <div className="w-full h-1 bg-gray-200 rounded"></div>
            <div className="w-3/4 h-1 bg-gray-200 rounded"></div>
          </div>
        </div>
      )
    },
    {
      id: 'professional',
      name: 'Executive Premium',
      description: 'Sophisticated two-column layout for senior positions',
      icon: <Briefcase className="w-8 h-8" />,
      color: 'from-purple-500 via-violet-600 to-indigo-600',
      bgGradient: 'from-purple-50 via-violet-50 to-indigo-50',
      borderColor: 'border-purple-200',
      popular: false,
      preview: (
        <div className="w-full h-32 bg-white rounded-lg shadow-lg relative overflow-hidden flex">
          <div className="w-1/3 bg-purple-800 relative">
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-6 h-6 bg-purple-600 rounded-full"></div>
            <div className="absolute top-10 left-1 right-1 space-y-1">
              <div className="w-full h-1 bg-purple-300 rounded"></div>
              <div className="w-3/4 h-1 bg-purple-300 rounded"></div>
            </div>
          </div>
          <div className="flex-1 p-2 space-y-1">
            <div className="w-full h-1 bg-purple-200 rounded"></div>
            <div className="w-4/5 h-1 bg-gray-200 rounded"></div>
            <div className="w-3/5 h-1 bg-gray-200 rounded"></div>
            <div className="w-full h-1 bg-purple-200 rounded mt-2"></div>
            <div className="w-2/3 h-1 bg-gray-200 rounded"></div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <Palette className="w-8 h-8 text-indigo-900" />
        </div>
        <h3 className="text-3xl font-bold text-gray-900 mb-3">
          Choose Your Perfect Template
        </h3>
        <p className="text-gray-600 text-lg">
          Select a design that showcases your hospitality expertise
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {templates.map((template) => (
          <Card key={template.id} className={`relative p-6 hover:shadow-2xl transition-all duration-500 cursor-pointer group hover:-translate-y-2 bg-gradient-to-br ${template.bgGradient} ${template.borderColor} border-2 hover:border-opacity-60`}>
            {template.popular && (
              <div className="absolute -top-3 -right-3 bg-gradient-to-r from-amber-400 to-yellow-500 text-indigo-900 px-3 py-1 rounded-full text-xs font-bold shadow-lg z-10">
                ⭐ Popular
              </div>
            )}
            
            <div className="text-center">
              {/* Template Preview */}
              <div className="mb-6 group-hover:scale-105 transition-transform duration-300">
                {template.preview}
              </div>
              
              {/* Icon */}
              <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${template.color} flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                <div className="text-white">
                  {template.icon}
                </div>
              </div>
              
              <h4 className="font-bold text-xl text-gray-900 mb-2">{template.name}</h4>
              <p className="text-sm text-gray-600 mb-6 leading-relaxed">{template.description}</p>
              
              <Button
                onClick={() => onTemplateSelect(template.id)}
                className={`w-full bg-gradient-to-r ${template.color} hover:opacity-90 text-white font-semibold py-3 px-6 transition-all duration-300 shadow-lg hover:shadow-xl`}
              >
                Select Template
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default TemplateSelector;
