
import React from 'react';
import Header from '@/components/Header';
import { Check, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/contexts/LanguageContext';

const Pricing = () => {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header />
      
      <main className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-6">
              {t('pricing.title')}
            </h1>
            <p className="text-xl text-gray-600">
              {t('pricing.description')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-3xl mx-auto">
            {/* Free Plan */}
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-200">
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{t('pricing.free')}</h3>
                <div className="text-4xl font-bold text-blue-600 mb-2">€0</div>
                <p className="text-gray-600">{t('pricing.free.description')}</p>
              </div>
              
              <ul className="space-y-3 mb-8">
                <li className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-500" />
                  <span className="text-gray-700">{t('pricing.feature1')}</span>
                </li>
                <li className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-500" />
                  <span className="text-gray-700">{t('pricing.feature2')}</span>
                </li>
                <li className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-500" />
                  <span className="text-gray-700">{t('pricing.feature3')}</span>
                </li>
                <li className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-500" />
                  <span className="text-gray-700">{t('pricing.feature4')}</span>
                </li>
              </ul>
              
              <Button className="w-full" variant="outline">
                {t('pricing.cta.free')}
              </Button>
            </div>

            {/* Pro Plan */}
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl p-8 shadow-lg text-white relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <div className="bg-yellow-400 text-gray-900 px-4 py-1 rounded-full text-sm font-semibold flex items-center gap-1">
                  <Star className="w-4 h-4" />
                  {t('pricing.coming.soon')}
                </div>
              </div>
              
              <div className="text-center mb-6">
                <h3 className="text-2xl font-bold mb-2">{t('pricing.pro')}</h3>
                <div className="text-4xl font-bold mb-2">€9.99</div>
                <p className="text-blue-100">{t('pricing.pro.description')}</p>
              </div>
              
              <ul className="space-y-3 mb-8">
                <li className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-400" />
                  <span>{t('pricing.feature5')}</span>
                </li>
                <li className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-400" />
                  <span>{t('pricing.feature6')}</span>
                </li>
                <li className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-400" />
                  <span>{t('pricing.feature7')}</span>
                </li>
                <li className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-400" />
                  <span>{t('pricing.feature8')}</span>
                </li>
                <li className="flex items-center gap-3">
                  <Check className="w-5 h-5 text-green-400" />
                  <span>{t('pricing.feature9')}</span>
                </li>
              </ul>
              
              <Button className="w-full bg-white text-blue-600 hover:bg-gray-100">
                {t('pricing.cta.notify')}
              </Button>
            </div>
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600">
              {t('pricing.beta')}
            </p>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Pricing;
