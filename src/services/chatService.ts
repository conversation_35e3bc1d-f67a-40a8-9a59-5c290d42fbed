
import { supabase } from '@/integrations/supabase/client';
import { UserProfile } from '@/types/UserProfile';
import { generateLocalResponse, ConversationResponse } from './conversationService';

/**
 * CENTRALIZED CHAT SERVICE - Single source of truth for chat processing
 * Handles both Edge Function calls and local fallbacks
 */

export const processUserInput = async (
  userInput: string,
  conversationState: string,
  currentProfile: UserProfile,
  conversationHistory?: Array<{content: string, isUser: boolean}>
): Promise<ConversationResponse> => {
  try {
    // Try Edge Function first
    const response = await processUserInputViaEdgeFunction(
      userInput,
      conversationState,
      currentProfile,
      conversationHistory
    );
    return response;
  } catch (edgeFunctionError) {
    console.log('Edge function failed, using local processing:', edgeFunctionError);
    // Fallback to local processing
    return generateLocalResponse(userInput, conversationState, currentProfile);
  }
};

export const processUserInputViaEdgeFunction = async (
  userInput: string,
  conversationState: string,
  currentProfile: UserProfile,
  conversationHistory?: Array<{content: string, isUser: boolean}>
): Promise<ConversationResponse> => {
  try {
    const { data, error } = await supabase.functions.invoke('gemini-chat', {
      body: {
        userInput,
        conversationState,
        currentProfile,
        conversationHistory: conversationHistory || []
      }
    });

    if (error) {
      throw error;
    }

    return {
      message: data.message,
      userProfile: data.userProfile,
      nextState: data.nextState
    };
  } catch (error) {
    console.error('Error calling chat service:', error);
    throw error;
  }
};
