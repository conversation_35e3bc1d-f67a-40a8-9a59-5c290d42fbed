import { UserProfile, Skill, Language, WorkExperience, Education, Certification } from '@/types/UserProfile';

/**
 * SINGLE SOURCE OF TRUTH for all profile-related operations
 * This service handles profile validation, merging, and state management
 */

export const isProfileComplete = (profile: UserProfile): boolean => {
  return (
    profile.personalInfo.fullName.length > 0 &&
    profile.personalInfo.email.length > 0 &&
    profile.personalInfo.phone.length > 0 &&
    profile.personalInfo.address.length > 0 &&
    profile.personalInfo.nationality.length > 0 &&
    profile.workExperience.length > 0 &&
    profile.skills.length > 0 &&
    profile.languages.length > 0
  );
};

export const mergeProfileData = (existing: UserProfile, newData: Partial<UserProfile>): UserProfile => {
  if (!newData) return existing;
  if (!existing) return newData as UserProfile;

  return {
    personalInfo: {
      fullName: newData.personalInfo?.fullName || existing.personalInfo?.fullName || '',
      email: newData.personalInfo?.email || existing.personalInfo?.email || '',
      phone: newData.personalInfo?.phone || existing.personalInfo?.phone || '',
      address: newData.personalInfo?.address || existing.personalInfo?.address || '',
      nationality: newData.personalInfo?.nationality || existing.personalInfo?.nationality || ''
    },
    workExperience: newData.workExperience?.length > 0 ? 
      [...existing.workExperience.filter(exp => !newData.workExperience!.some(newExp => newExp.jobTitle === exp.jobTitle)), ...newData.workExperience] : 
      existing.workExperience,
    skills: newData.skills?.length > 0 ? 
      [...existing.skills.filter(skill => !newData.skills!.some(newSkill => newSkill.name === skill.name)), ...newData.skills] : 
      existing.skills,
    languages: newData.languages?.length > 0 ? 
      [...existing.languages.filter(lang => !newData.languages!.some(newLang => newLang.name === lang.name)), ...newData.languages] : 
      existing.languages,
    education: newData.education?.length > 0 ? 
      [...existing.education.filter(edu => !newData.education!.some(newEdu => newEdu.degree === edu.degree)), ...newData.education] : 
      existing.education,
    certifications: newData.certifications?.length > 0 ?
      [...existing.certifications.filter(cert => !newData.certifications!.some(newCert => newCert.name === cert.name)), ...newData.certifications] : 
      existing.certifications
  };
};

export const extractUserProfile = (userInput: string, state: string, currentProfile: UserProfile): Partial<UserProfile> => {
  const profile: Partial<UserProfile> = {
    personalInfo: {},
    workExperience: [],
    skills: [],
    languages: [],
    education: [],
    certifications: []
  };

  const lowerInput = userInput.toLowerCase();

  switch (state) {
    case 'personal_info':
      if (!currentProfile.personalInfo.fullName) {
        const nameWords = userInput.trim().split(' ').filter(w => w.length > 1);
        if (nameWords.length >= 2) {
          const formattedName = nameWords.map(word => 
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          ).join(' ');
          profile.personalInfo!.fullName = formattedName;
        }
      } else if (!currentProfile.personalInfo.email) {
        const emailMatch = userInput.match(/\S+@\S+\.\S+/);
        if (emailMatch) {
          profile.personalInfo!.email = emailMatch[0];
        }
      } else if (!currentProfile.personalInfo.phone) {
        const phoneMatch = userInput.match(/[\+]?[(]?[\d\s\-\(\)]{8,}/);
        if (phoneMatch) {
          profile.personalInfo!.phone = phoneMatch[0].trim();
        }
      }
      break;

    case 'contact_details':
      if (!currentProfile.personalInfo.address) {
        const hasStreetAndNumber = /\d+/.test(userInput) && userInput.length > 10;
        const hasGermanCity = /berlin|hamburg|münchen|munich|köln|cologne|frankfurt|stuttgart|düsseldorf|dortmund|essen|leipzig|bremen|dresden|hannover|nürnberg|nuremberg/i.test(userInput);
        const hasPostalCode = /\d{5}/.test(userInput);
        const hasStreetKeywords = /str|straße|platz|weg|allee|ring|damm|ufer/i.test(userInput);

        if ((hasStreetAndNumber || hasGermanCity || hasPostalCode || hasStreetKeywords) && userInput.length > 15) {
          profile.personalInfo!.address = userInput.trim();
        }
      } else if (!currentProfile.personalInfo.nationality) {
        const nationalities: Record<string, string> = {
          'china': 'Chinese', 'chinese': 'Chinese', 'german': 'German', 'germany': 'German',
          'american': 'American', 'usa': 'American', 'british': 'British', 'uk': 'British',
          'spanish': 'Spanish', 'spain': 'Spanish', 'french': 'French', 'france': 'French',
          'italian': 'Italian', 'italy': 'Italian', 'turkish': 'Turkish', 'turkey': 'Turkish',
          'polish': 'Polish', 'poland': 'Polish', 'ukrainian': 'Ukrainian', 'ukraine': 'Ukrainian',
          'syrian': 'Syrian', 'syria': 'Syrian', 'afghan': 'Afghan', 'afghanistan': 'Afghan',
          'romanian': 'Romanian', 'romania': 'Romanian', 'bulgarian': 'Bulgarian', 'bulgaria': 'Bulgarian',
          'indian': 'Indian', 'india': 'Indian', 'pakistani': 'Pakistani', 'pakistan': 'Pakistani'
        };

        const foundKey = Object.keys(nationalities).find(key => lowerInput.includes(key));
        if (foundKey) {
          profile.personalInfo!.nationality = nationalities[foundKey];
        }
      }
      break;

    case 'work_experience':
      if (userInput.length > 5) {
        const jobTitles = ['bartender', 'waiter', 'waitress', 'cleaner', 'kitchen helper', 'cook', 'chef', 'receptionist', 'housekeeper', 'server'];
        const foundJobTitle = jobTitles.find(title => lowerInput.includes(title));
        
        let jobTitle = foundJobTitle ? foundJobTitle.charAt(0).toUpperCase() + foundJobTitle.slice(1) : 'Hospitality Worker';
        if (lowerInput.includes('chef') || lowerInput.includes('cook')) {
          jobTitle = 'Chef';
        } else if (lowerInput.includes('bartender')) {
          jobTitle = 'Bartender';
        } else if (lowerInput.includes('waiter') || lowerInput.includes('waitress') || lowerInput.includes('server')) {
          jobTitle = 'Waiter';
        } else if (lowerInput.includes('cleaner') || lowerInput.includes('housekeeper')) {
          jobTitle = 'Housekeeper';
        }

        let company = 'Restaurant/Hotel';
        const companyMatch = userInput.match(/(?:at|in|for)\s+([A-Z][a-zA-Z\s]+(?:Hotel|Restaurant|Bar|Cafe|Resort|Club))/i);
        if (companyMatch) {
          company = companyMatch[1];
        }

        let duration = '2020-Present';
        const workYearMatch = userInput.match(/(\d{4})\s*[-to]*\s*(\d{4})?\s*(?:to present|present)?/i);
        if (workYearMatch) {
          duration = workYearMatch[2] ? `${workYearMatch[1]}-${workYearMatch[2]}` : `${workYearMatch[1]}-Present`;
        }
        
        profile.workExperience!.push({
          jobTitle,
          company,
          duration,
          description: `Worked as ${jobTitle} providing excellent customer service and maintaining high standards of hospitality.`
        });
      }
      break;

    case 'skills':
      const skillKeywords: Record<string, string[]> = {
        'Customer Service': ['customer', 'service', 'guest', 'serving'],
        'Communication': ['communication', 'talking', 'speaking', 'language'],
        'Teamwork': ['team', 'cooperation', 'together', 'group'],
        'Bartending': ['cocktail', 'drink', 'bar', 'mixing', 'alcohol'],
        'Cooking': ['cook', 'food', 'kitchen', 'prepare', 'recipe'],
        'Cash Handling': ['money', 'cash', 'payment', 'register', 'till'],
        'Cleaning': ['clean', 'tidy', 'sanitize', 'housekeeping'],
        'Multitasking': ['multitask', 'busy', 'many things', 'quick'],
        'Leadership': ['lead', 'manage', 'supervise', 'boss', 'train']
      };
      
      Object.entries(skillKeywords).forEach(([skill, keywords]) => {
        if (keywords.some(keyword => lowerInput.includes(keyword))) {
          let level: 'Beginner' | 'Intermediate' | 'Advanced' | 'Expert' = 'Intermediate';
          if (lowerInput.includes('very good') || lowerInput.includes('expert') || lowerInput.includes('excellent')) {
            level = 'Advanced';
          } else if (lowerInput.includes('basic') || lowerInput.includes('learning') || lowerInput.includes('beginner')) {
            level = 'Beginner';
          }
          profile.skills!.push({ name: skill, level });
        }
      });
      break;

    case 'languages':
      const langKeywords = ['english', 'german', 'turkish', 'arabic', 'spanish', 'french', 'italian', 'polish', 'russian', 'romanian', 'bulgarian', 'chinese', 'mandarin'];
      const levelKeywords: Record<string, string[]> = {
        'Basic': ['basic', 'little', 'beginner', 'some'],
        'Conversational': ['good', 'okay', 'conversational', 'intermediate'],
        'Fluent': ['fluent', 'very good', 'excellent', 'very well', 'advanced'],
        'Native': ['native', 'mother tongue', 'first language']
      };

      langKeywords.forEach(lang => {
        if (lowerInput.includes(lang)) {
          let level: 'Basic' | 'Conversational' | 'Fluent' | 'Native' = 'Conversational';
          Object.entries(levelKeywords).forEach(([levelName, keywords]) => {
            if (keywords.some(keyword => lowerInput.includes(keyword))) {
              level = levelName as 'Basic' | 'Conversational' | 'Fluent' | 'Native';
            }
          });
          
          let displayName = lang.charAt(0).toUpperCase() + lang.slice(1);
          if (lang === 'mandarin') displayName = 'Chinese';
          
          profile.languages!.push({ 
            name: displayName, 
            level 
          });
        }
      });
      break;
      
    case 'education':
      let degree = 'High School';
      if (lowerInput.includes('university') || lowerInput.includes('bachelor') || lowerInput.includes('master')) {
        degree = 'University Degree';
      } else if (lowerInput.includes('vocational') || lowerInput.includes('training') || lowerInput.includes('apprentice')) {
        degree = 'Vocational Training';
      }

      const educationYearMatch = userInput.match(/(\d{4})/);
      profile.education!.push({
        degree,
        institution: 'Educational Institution',
        year: educationYearMatch ? educationYearMatch[1] : '2020'
      });
      break;

    case 'certifications':
      if (!lowerInput.includes('no') && !lowerInput.includes('none')) {
        const certTypes: Record<string, string[]> = {
          'Food Safety': ['food safety', 'hygiene', 'haccp'],
          'First Aid': ['first aid', 'cpr', 'medical'],
          'Bartending': ['bartending', 'cocktail', 'drink'],
          'Language': ['language', 'german', 'english']
        };

        Object.entries(certTypes).forEach(([certName, keywords]) => {
          if (keywords.some(keyword => lowerInput.includes(keyword))) {
            profile.certifications!.push({
              name: certName + ' Certificate',
              issuer: 'Certification Authority',
              year: new Date().getFullYear().toString()
            });
          }
        });
      }
      break;
  }

  return profile;
};

export const getNextConversationState = (currentState: string, extractedProfile: Partial<UserProfile>, currentProfile: UserProfile): string => {
  const mergedProfile = mergeProfileData(currentProfile, extractedProfile);
  const personalInfo = mergedProfile.personalInfo;

  // Personal info phase
  if (!personalInfo.fullName || !personalInfo.email || !personalInfo.phone) {
    return 'personal_info';
  }

  // Contact details phase
  if (!personalInfo.address || !personalInfo.nationality) {
    return 'contact_details';
  }

  // Work experience phase
  if (!mergedProfile.workExperience || mergedProfile.workExperience.length === 0) {
    return 'work_experience';
  }

  // Skills phase
  if (!mergedProfile.skills || mergedProfile.skills.length === 0) {
    return 'skills';
  }

  // Languages phase
  if (!mergedProfile.languages || mergedProfile.languages.length === 0) {
    return 'languages';
  }

  // Education phase
  if (!mergedProfile.education || mergedProfile.education.length === 0) {
    return 'education';
  }

  // Certifications phase
  if (!mergedProfile.certifications || mergedProfile.certifications.length === 0) {
    return 'certifications';
  }

  // All complete
  return 'completion';
};

export const createEmptyProfile = (): UserProfile => ({
  personalInfo: {
    fullName: '',
    email: '',
    phone: '',
    address: '',
    nationality: ''
  },
  workExperience: [],
  skills: [],
  languages: [],
  education: [],
  certifications: []
});
