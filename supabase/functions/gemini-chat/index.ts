
// @ts-ignore - Deno imports work in Supabase Edge Functions
import "https://deno.land/x/xhr@0.1.0/mod.ts";
// @ts-ignore - Deno imports work in Supabase Edge Functions
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Helper function to create profile summary for AI context
const createProfileSummary = (profile: any): string => {
  console.log('Creating profile summary for:', profile);
  if (!profile || !profile.personalInfo) {
    return "No profile information collected yet";
  }

  const info = profile.personalInfo;
  const parts: string[] = [];

  if (info.fullName) parts.push(`Name: ${info.fullName}`);
  if (info.email) parts.push(`Email: ${info.email}`);
  if (info.phone) parts.push(`Phone: ${info.phone}`);
  if (info.address) parts.push(`Address: ${info.address}`);
  if (info.nationality) parts.push(`Nationality: ${info.nationality}`);

  if (profile.workExperience && profile.workExperience.length > 0) {
    parts.push(`Work Experience: ${profile.workExperience.length} job(s) recorded`);
  }

  if (profile.skills && profile.skills.length > 0) {
    parts.push(`Skills: ${profile.skills.length} skill(s) recorded`);
  }

  if (profile.languages && profile.languages.length > 0) {
    parts.push(`Languages: ${profile.languages.length} language(s) recorded`);
  }

  return parts.length > 0 ? parts.join(", ") : "No profile information collected yet";
};

// Helper function to create conversation context
const createConversationContext = (state: string, profile: any): string => {
  const info = profile?.personalInfo || {};
  console.log('Creating conversation context for state:', state, 'with profile info:', info);

  switch (state) {
    case 'role_selection':
      return "Ask what type of hospitality job they want (waiter, chef, bartender, etc.)";

    case 'personal_info':
      // Enforce strict order: name → email → phone
      if (!info.fullName) {
        return "Ask for their FULL NAME first. Keep it short and friendly. Include tip about using first and last name.";
      }
      if (!info.email) {
        return "Ask for their EMAIL ADDRESS next. Keep it short and mention it's for employers to contact them.";
      }
      if (!info.phone) {
        return "Ask for their PHONE NUMBER next. Keep it short and mention it's for direct contact.";
      }
      return "All personal info collected - move to address/nationality";

    case 'contact_details':
      // Enforce strict order: address → nationality
      if (!info.address) {
        return "Ask for their ADDRESS in Germany next. Keep it short and include example format.";
      }
      if (!info.nationality) {
        return "Ask for their NATIONALITY next. Keep it short and give examples.";
      }
      return "All contact details collected - move to work experience";

    case 'work_experience':
      return "Ask for ONE piece of work info at a time. Keep questions SHORT and bold important words. Give examples. Current priority: job title, then company, then duration, then responsibilities.";

    case 'skills':
      return "Ask for hospitality skills. Keep it SHORT, bold key words, give examples like: customer service, cleaning, teamwork.";

    case 'languages':
      return "Ask for languages and levels. Keep it SHORT, bold key words, give examples like: German - Advanced, English - Basic.";

    case 'education':
      return "Ask for education. Keep it SHORT, bold key words, give examples like: High School, Hotel Management Course.";

    case 'certifications':
      return "Ask for certificates. Keep it SHORT, bold key words, give examples like: Food Safety, First Aid.";

    case 'complete':
      return "Congratulate them - their CV is complete!";

    default:
      return "Continue collecting CV information";
  }
};

// Helper function to create conversation history context
const createHistoryContext = (history: Array<{content: string, isUser: boolean}>): string => {
  if (!history || history.length === 0) {
    return "This is the start of the conversation";
  }

  // Get last 4 exchanges (2 user + 2 assistant messages)
  const recentHistory = history.slice(-4);
  const formattedHistory = recentHistory.map(msg =>
    `${msg.isUser ? 'User' : 'Assistant'}: ${msg.content}`
  ).join('\n');

  return `Recent conversation:\n${formattedHistory}`;
};

// Local response handler to prevent duplicate questions
const handleLocalResponse = (userInput: string, conversationState: string, currentProfile: any): any => {
  console.log('=== handleLocalResponse ===');
  console.log('userInput:', userInput);
  console.log('conversationState:', conversationState);
  console.log('currentProfile:', currentProfile);

  // AGGRESSIVE local handling to enforce correct order
  // Override AI completely for critical flow control

  if (conversationState === 'personal_info') {
    console.log('handleLocalResponse - personal_info state');
    console.log('currentProfile?.personalInfo:', currentProfile?.personalInfo);

    // FORCE correct order - always return a response for personal_info
    if (!currentProfile?.personalInfo?.fullName) {
      console.log('No fullName found, checking for name in input');
      // More flexible name validation
      const nameWords = userInput.trim().split(/\s+/).filter((w: string) => w.length > 0);
      if (nameWords.length >= 2 && nameWords.every((word: string) => /^[a-zA-ZÀ-ÿ\u0100-\u017F\u0180-\u024F\u1E00-\u1EFF]+$/.test(word))) {
        const formattedName = nameWords.map((word: string) =>
          word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        ).join(' ');
        console.log('Name detected, returning local response for email');
        return {
          message: `Perfect! Hello ${formattedName}! 👋\n\nWhat's your email address?\n\n💡 Employers will use this to contact you`
        };
      }
      // FORCE asking for name if not provided
      console.log('Forcing name question');
      return {
        message: `What's your full name?\n\n💡 Please include both first and last name`
      };
    }

    if (!currentProfile?.personalInfo?.email) {
      console.log('No email found, checking for email in input');
      // More flexible email validation
      const emailMatch = userInput.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
      if (emailMatch) {
        console.log('Email detected, returning local response for phone');
        return {
          message: `Great! 📧 ${emailMatch[0]}\n\nWhat's your phone number?\n\n💡 Employers will call you for interviews`
        };
      }
      // FORCE asking for email if not provided
      console.log('Forcing email question');
      return {
        message: `What's your email address?\n\n💡 Employers will use this to contact you`
      };
    }

    if (!currentProfile?.personalInfo?.phone) {
      console.log('No phone found, checking for phone in input');
      // More flexible phone validation - use same regex as extractUserProfile
      const phoneMatch = userInput.match(/[\+]?[(]?[\d\s\-\(\)\.]{8,}/);
      if (phoneMatch) {
        console.log('Phone detected, returning local response for address');
        return {
          message: `Perfect! 📱\n\nWhat's your address in Germany?\n\n💡 Example: Hauptstraße 123, 10115 Berlin`
        };
      }
      // FORCE asking for phone if not provided
      console.log('Forcing phone question');
      return {
        message: `What's your phone number?\n\n💡 Employers will call you for interviews`
      };
    }

    // If we have all personal info, move to contact details
    console.log('All personal info collected, should move to contact_details');
    return {
      message: `Great! ✅\n\nWhat's your address in Germany?\n\n💡 Example: Hauptstraße 123, 10115 Berlin`
    };
  }

  // Handle contact_details state
  if (conversationState === 'contact_details') {
    console.log('=== contact_details state ===');

    if (!currentProfile?.personalInfo?.address) {
      // More flexible address validation - check for street name, number, and city
      const hasStreetAndNumber = /\d+/.test(userInput) && userInput.length > 10;
      const hasGermanCity = /berlin|hamburg|münchen|munich|köln|cologne|frankfurt|stuttgart|düsseldorf|dortmund|essen|leipzig|bremen|dresden|hannover|nürnberg|nuremberg/i.test(userInput);
      const hasPostalCode = /\d{5}/.test(userInput);

      if ((hasStreetAndNumber || hasGermanCity || hasPostalCode) && userInput.length > 15) {
        return {
          message: `Great! 🏠\n\nWhat's your nationality?\n\n💡 Examples: German, Chinese, Turkish, Polish`
        };
      }
      // FORCE asking for address if not provided
      return {
        message: `What's your address in Germany?\n\n💡 Example: Hauptstraße 123, 10115 Berlin`
      };
    }
  }

  // Only handle nationality if it's clearly identifiable
  if (conversationState === 'contact_details' && !currentProfile?.personalInfo?.nationality) {
    const nationalities: Record<string, string> = {
      'china': 'Chinese', 'chinese': 'Chinese',
      'german': 'German', 'germany': 'German', 'deutschland': 'German',
      'american': 'American', 'usa': 'American', 'united states': 'American',
      'british': 'British', 'uk': 'British', 'england': 'British',
      'spanish': 'Spanish', 'spain': 'Spanish',
      'french': 'French', 'france': 'French',
      'italian': 'Italian', 'italy': 'Italian',
      'turkish': 'Turkish', 'turkey': 'Turkish',
      'poland': 'Polish', 'polish': 'Polish',
      'ukrainian': 'Ukrainian', 'ukraine': 'Ukrainian',
      'syrian': 'Syrian', 'syria': 'Syrian',
      'afghan': 'Afghan', 'afghanistan': 'Afghan',
      'romanian': 'Romanian', 'romania': 'Romanian',
      'bulgaria': 'Bulgarian', 'bulgarian': 'Bulgarian',
      'indian': 'Indian', 'india': 'Indian',
      'pakistani': 'Pakistani', 'pakistan': 'Pakistani'
    };

    const inputLower = userInput.toLowerCase();
    const foundKey = Object.keys(nationalities).find(key => inputLower.includes(key));
    if (foundKey) {
      return {
        message: `Perfect! Now let's get your work experience.\n\nWhat was your job title at your most recent hospitality job?\n\n💡 Examples: Bartender, Waiter, Cleaner, Kitchen Helper`
      };
    }
  }

  // Handle work_experience state - prevent loops
  if (conversationState === 'work_experience') {
    console.log('=== work_experience state ===');
    const hasWorkExperience = currentProfile?.workExperience && currentProfile.workExperience.length > 0;

    if (hasWorkExperience) {
      console.log('Work experience already collected, moving to skills');
      return {
        message: `Excellent! Now let's get your skills.\n\nWhat hospitality skills do you have?\n\n💡 Examples: Customer service, Cleaning, Teamwork, Food prep`
      };
    }
  }

  // Handle skills state - prevent loops
  if (conversationState === 'skills') {
    console.log('=== skills state ===');
    const hasSkills = currentProfile?.skills && currentProfile.skills.length > 0;

    if (hasSkills) {
      console.log('Skills already collected, moving to languages');
      return {
        message: `Great! Now let's get your languages.\n\nWhat languages do you speak and how well?\n\n💡 Examples: German - Advanced, English - Basic`
      };
    }
  }

  // Handle languages state - prevent loops
  if (conversationState === 'languages') {
    console.log('=== languages state ===');
    const hasLanguages = currentProfile?.languages && currentProfile.languages.length > 0;

    if (hasLanguages) {
      console.log('Languages already collected, moving to education');
      return {
        message: `Perfect! Now let's get your education.\n\nWhat's your highest education level?\n\n💡 Examples: High School, University, Hotel Management Course`
      };
    }
  }

  // Handle education state - prevent loops
  if (conversationState === 'education') {
    console.log('=== education state ===');
    const hasEducation = currentProfile?.education && currentProfile.education.length > 0;

    if (hasEducation) {
      console.log('Education already collected, moving to certifications');
      return {
        message: `Excellent! Last step - certificates.\n\nDo you have any certificates for hospitality work?\n\n💡 Examples: Food Safety, First Aid, Language Certificate\n\n✨ Say "none" if you don't have any`
      };
    }
  }

  // Handle certifications state - prevent loops
  if (conversationState === 'certifications') {
    console.log('=== certifications state ===');
    const hasCertifications = currentProfile?.certifications && currentProfile.certifications.length > 0;

    if (hasCertifications) {
      console.log('Certifications already collected, completing CV');
      return {
        message: `Fantastic! Your CV is now complete. I have all the information needed to create a professional resume for you. You can see your completed CV in the preview on the right. Good luck with your job applications!`
      };
    }
  }

  // Let AI handle everything else for more natural conversation
  return null;
};

// Extract user profile from input
const extractUserProfile = (userInput: string, state: string, currentProfile: any): any => {
  const profile: any = {
    personalInfo: {},
    workExperience: [],
    skills: [],
    languages: [],
    education: [],
    certifications: []
  };

  console.log('Extracting profile for state:', state, 'Input:', userInput);

  switch (state) {
    case 'personal_info':
      // Extract name if not already collected and present in input
      if (!currentProfile?.personalInfo?.fullName) {
        const nameWords = userInput.trim().split(' ').filter((w: string) => w.length > 1);
        if (nameWords.length >= 2) {
          // Capitalize first letter of each word
          const formattedName = nameWords.map((word: string) =>
            word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          ).join(' ');
          profile.personalInfo.fullName = formattedName;
        }
      }

      // Extract email if not already collected and present in input
      if (!currentProfile?.personalInfo?.email) {
        const emailMatch = userInput.match(/\S+@\S+\.\S+/);
        if (emailMatch) {
          profile.personalInfo.email = emailMatch[0];
        }
      }

      // Extract phone if not already collected and present in input
      if (!currentProfile?.personalInfo?.phone) {
        const phoneMatch = userInput.match(/[\+]?[(]?[\d\s\-\(\)\.]{8,}/);
        if (phoneMatch) {
          profile.personalInfo.phone = phoneMatch[0].trim();
        }
      }
      break;

    case 'contact_details':
      if (!currentProfile?.personalInfo?.address) {
        // More flexible address validation - check for street name, number, and city
        const hasStreetAndNumber = /\d+/.test(userInput) && userInput.length > 10;
        const hasGermanCity = /berlin|hamburg|münchen|munich|köln|cologne|frankfurt|stuttgart|düsseldorf|dortmund|essen|leipzig|bremen|dresden|hannover|nürnberg|nuremberg/i.test(userInput);
        const hasPostalCode = /\d{5}/.test(userInput);
        const hasStreetKeywords = /str|straße|platz|weg|allee|ring|damm|ufer/i.test(userInput);

        if ((hasStreetAndNumber || hasGermanCity || hasPostalCode || hasStreetKeywords) && userInput.length > 15) {
          profile.personalInfo.address = userInput.trim();
        }
      } else if (!currentProfile?.personalInfo?.nationality) {
        const nationalities: Record<string, string> = {
          'china': 'Chinese', 'chinese': 'Chinese', 'german': 'German', 'germany': 'German', 'deutschland': 'German',
          'american': 'American', 'usa': 'American', 'united states': 'American',
          'british': 'British', 'uk': 'British', 'england': 'British',
          'spanish': 'Spanish', 'spain': 'Spanish',
          'french': 'French', 'france': 'French',
          'italian': 'Italian', 'italy': 'Italian',
          'turkish': 'Turkish', 'turkey': 'Turkish',
          'polish': 'Polish', 'poland': 'Polish',
          'ukrainian': 'Ukrainian', 'ukraine': 'Ukrainian',
          'syrian': 'Syrian', 'syria': 'Syrian',
          'afghan': 'Afghan', 'afghanistan': 'Afghan',
          'romanian': 'Romanian', 'romania': 'Romanian',
          'bulgarian': 'Bulgarian', 'bulgaria': 'Bulgarian',
          'indian': 'Indian', 'india': 'Indian',
          'pakistani': 'Pakistani', 'pakistan': 'Pakistani'
        };

        const inputLower = userInput.toLowerCase();
        const foundKey = Object.keys(nationalities).find((key: string) => inputLower.includes(key));
        if (foundKey) {
          profile.personalInfo.nationality = nationalities[foundKey];
        }
      }
      break;

    case 'work_experience':
      // Extract work experience from user input
      if (userInput.length > 5) {
        // Look for job titles, companies, and durations
        const jobTitles = ['bartender', 'waiter', 'waitress', 'cleaner', 'kitchen helper', 'cook', 'chef', 'receptionist', 'housekeeper', 'server'];
        const foundJobTitle = jobTitles.find(title => userInput.toLowerCase().includes(title));

        if (foundJobTitle || userInput.length > 10) {
          // Create a work experience entry
          const workEntry: any = {
            jobTitle: foundJobTitle ? foundJobTitle.charAt(0).toUpperCase() + foundJobTitle.slice(1) : userInput.trim(),
            company: '',
            duration: '',
            responsibilities: ''
          };

          // Try to extract company name (look for "at", "in", "for" keywords)
          const companyMatch = userInput.match(/(?:at|in|for)\s+([^,.\n]+)/i);
          if (companyMatch) {
            workEntry.company = companyMatch[1].trim();
          }

          // Try to extract duration (look for years, months, dates)
          const durationMatch = userInput.match(/(\d+\s*(?:year|month|yr|mo)s?|\d{4}.*\d{4}|june.*end|from.*to)/i);
          if (durationMatch) {
            workEntry.duration = durationMatch[0].trim();
          }

          profile.workExperience.push(workEntry);
        }
      }
      break;

    case 'skills':
      // Extract skills from user input
      if (userInput.length > 3) {
        const skillKeywords = ['customer service', 'cleaning', 'teamwork', 'communication', 'food prep', 'cooking', 'bartending', 'organization', 'time management', 'multitasking'];
        const foundSkills = skillKeywords.filter(skill => userInput.toLowerCase().includes(skill.toLowerCase()));

        if (foundSkills.length > 0) {
          profile.skills = foundSkills.map(skill => ({
            name: skill.charAt(0).toUpperCase() + skill.slice(1),
            level: 'Intermediate'
          }));
        } else {
          // Split by commas and clean up
          const skills = userInput.split(/[,\n]/).map(s => s.trim()).filter(s => s.length > 2);
          profile.skills = skills.map(skill => ({
            name: skill.charAt(0).toUpperCase() + skill.slice(1),
            level: 'Intermediate'
          }));
        }
      }
      break;

    case 'languages':
      // Extract languages from user input
      if (userInput.length > 3) {
        const languagePattern = /([a-zA-Z]+)\s*[-:]?\s*(basic|intermediate|advanced|native|fluent|b1|b2|c1|c2|a1|a2)/gi;
        const matches = [...userInput.matchAll(languagePattern)];

        if (matches.length > 0) {
          profile.languages = matches.map(match => ({
            name: match[1].charAt(0).toUpperCase() + match[1].slice(1).toLowerCase(),
            level: match[2].charAt(0).toUpperCase() + match[2].slice(1).toLowerCase()
          }));
        } else {
          // Simple extraction - assume format like "German, English"
          const langs = userInput.split(/[,\n]/).map(s => s.trim()).filter(s => s.length > 1);
          profile.languages = langs.map(lang => ({
            name: lang.charAt(0).toUpperCase() + lang.slice(1).toLowerCase(),
            level: 'Conversational'
          }));
        }
      }
      break;

    case 'education':
      // Extract education from user input
      if (userInput.length > 3) {
        profile.education.push({
          degree: userInput.trim(),
          institution: '',
          year: ''
        });
      }
      break;

    case 'certifications':
      // Extract certifications from user input
      if (userInput.length > 3 && !userInput.toLowerCase().includes('none') && !userInput.toLowerCase().includes('no')) {
        const certs = userInput.split(/[,\n]/).map(s => s.trim()).filter(s => s.length > 2);
        profile.certifications = certs.map(cert => ({
          name: cert.charAt(0).toUpperCase() + cert.slice(1),
          issuer: '',
          year: ''
        }));
      }
      break;
  }

  console.log('Extracted profile:', profile);
  console.log('Current profile before merge:', currentProfile);
  return profile;
};

// Get next conversation state
const getNextConversationState = (currentState: string, extractedProfile: any, currentProfile: any): string => {
  console.log('=== getNextConversationState ===');
  console.log('currentState:', currentState);
  console.log('extractedProfile:', extractedProfile);
  console.log('currentProfile:', currentProfile);

  if (currentState === 'role_selection') {
    console.log('Moving from role_selection to personal_info');
    return 'personal_info';
  }

  if (currentState === 'personal_info') {
    const personalInfo = { ...currentProfile?.personalInfo, ...extractedProfile?.personalInfo };
    console.log('Merged personalInfo:', personalInfo);
    console.log('Checking completeness - fullName:', !!personalInfo.fullName, 'email:', !!personalInfo.email, 'phone:', !!personalInfo.phone);

    if (personalInfo.fullName && personalInfo.email && personalInfo.phone) {
      console.log('All personal info complete, moving to contact_details');
      return 'contact_details';
    }
    console.log('Personal info incomplete, staying in personal_info');
    return 'personal_info';
  }

  if (currentState === 'contact_details') {
    const personalInfo = { ...currentProfile?.personalInfo, ...extractedProfile?.personalInfo };
    console.log('Contact details check - address:', !!personalInfo.address, 'nationality:', !!personalInfo.nationality);

    if (personalInfo.address && personalInfo.nationality) {
      console.log('Contact details complete, moving to work_experience');
      return 'work_experience';
    }
    console.log('Contact details incomplete, staying in contact_details');
    return 'contact_details';
  }

  if (currentState === 'work_experience') {
    const workExperience = [...(currentProfile?.workExperience || []), ...(extractedProfile?.workExperience || [])];
    console.log('Work experience check - count:', workExperience.length);

    if (workExperience.length > 0) {
      console.log('Work experience complete, moving to skills');
      return 'skills';
    }
    console.log('Work experience incomplete, staying in work_experience');
    return 'work_experience';
  }

  if (currentState === 'skills') {
    const skills = [...(currentProfile?.skills || []), ...(extractedProfile?.skills || [])];
    console.log('Skills check - count:', skills.length);

    if (skills.length > 0) {
      console.log('Skills complete, moving to languages');
      return 'languages';
    }
    console.log('Skills incomplete, staying in skills');
    return 'skills';
  }

  if (currentState === 'languages') {
    const languages = [...(currentProfile?.languages || []), ...(extractedProfile?.languages || [])];
    console.log('Languages check - count:', languages.length);

    if (languages.length > 0) {
      console.log('Languages complete, moving to education');
      return 'education';
    }
    console.log('Languages incomplete, staying in languages');
    return 'languages';
  }

  if (currentState === 'education') {
    const education = [...(currentProfile?.education || []), ...(extractedProfile?.education || [])];
    console.log('Education check - count:', education.length);

    if (education.length > 0) {
      console.log('Education complete, moving to certifications');
      return 'certifications';
    }
    console.log('Education incomplete, staying in education');
    return 'education';
  }

  if (currentState === 'certifications') {
    const certifications = [...(currentProfile?.certifications || []), ...(extractedProfile?.certifications || [])];
    console.log('Certifications check - count:', certifications.length);

    if (certifications.length > 0) {
      console.log('Certifications complete, moving to complete');
      return 'complete';
    }
    console.log('Certifications incomplete, staying in certifications');
    return 'certifications';
  }

  console.log('No state change, returning:', currentState);
  return currentState;
};

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { userInput, conversationState, currentProfile, conversationHistory } = await req.json();

    console.log('=== NEW REQUEST ===');
    console.log('userInput:', userInput);
    console.log('conversationState:', conversationState);
    console.log('currentProfile:', JSON.stringify(currentProfile, null, 2));
    console.log('conversationHistory length:', conversationHistory?.length || 0);

    // @ts-ignore - Deno global is available in Supabase Edge Functions
    const apiKey = Deno.env.get('GEMINI_API_KEY');
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    // First check if we should handle this locally to prevent duplicate questions
    console.log('=== BEFORE LOCAL RESPONSE ===');
    console.log('userInput:', userInput);
    console.log('conversationState:', conversationState);
    console.log('currentProfile passed to handleLocalResponse:', JSON.stringify(currentProfile, null, 2));

    const localResponse = handleLocalResponse(userInput, conversationState, currentProfile);
    console.log('Local response result:', localResponse);

    if (localResponse) {
      console.log('Using local response:', localResponse.message);
      // Extract user profile from the input even for local responses
      const extractedProfile = extractUserProfile(userInput, conversationState, currentProfile);
      const nextState = getNextConversationState(conversationState, extractedProfile, currentProfile);
      console.log('Local response - nextState:', nextState);

      return new Response(JSON.stringify({
        message: localResponse.message,
        userProfile: extractedProfile,
        nextState: nextState
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }

    console.log('No local response, using AI');

    // Create comprehensive context-aware prompts
    const profileSummary = createProfileSummary(currentProfile);
    const conversationContext = createConversationContext(conversationState, currentProfile);
    const historyContext = createHistoryContext(conversationHistory || []);

    const systemPrompt = `You are an experienced career counselor specializing in helping hospitality workers in Germany find jobs. You are warm, encouraging, and professional.

CONTEXT:
- Current conversation stage: ${conversationState}
- User's current profile: ${profileSummary}
- What you need to collect: ${conversationContext}
- Recent conversation: ${historyContext}

PERSONALITY & STYLE:
- Be warm, encouraging, and supportive
- Use simple, clear language appropriate for hospitality workers
- Show genuine interest in their career goals
- Be patient and understanding with non-native speakers
- Never mention you are an AI or refer to technical terms

CONVERSATION RULES:
- Ask only ONE question at a time
- Build on what the user just said - acknowledge their input
- Reference previous conversation when relevant
- NEVER ask for information you already have in the profile
- If user gives incomplete info, ask for clarification gently
- If user goes off-topic, redirect kindly but firmly
- Validate and encourage good responses
- If user seems confused, rephrase your question differently

CRITICAL: NEVER ASK FOR INFORMATION ALREADY IN THE PROFILE SUMMARY ABOVE!

STRICT INFORMATION COLLECTION ORDER - FOLLOW EXACTLY:
1. Personal Info Stage: Name → Email → Phone (NEVER deviate from this order)
2. Contact Details Stage: Address → Nationality (NEVER deviate from this order)
3. Work Experience Stage: Previous jobs and experience
4. Skills Stage: Professional skills
5. Languages Stage: Language abilities
6. Education Stage: Educational background
7. Certifications Stage: Professional certifications

MANDATORY RULES:
- If name is already collected, NEVER ask for name again
- If email is already collected, NEVER ask for email again
- If phone is already collected, NEVER ask for phone again
- Follow the conversation guidance exactly - it tells you what to ask for next
- Ask for ONE piece of information at a time in the exact order specified

CURRENT USER INPUT: "${userInput}"

Respond naturally as a helpful career counselor would, acknowledging what they said and asking the next appropriate question based on your conversation stage and what information is still missing.`;

    // Try multiple models with retry logic
    const models = ['gemini-2.0-flash-exp', 'gemini-1.5-flash'];
    let response: Response | null = null;
    let lastError: Error | null = null;

    for (const modelName of models) {
      try {
        response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${apiKey}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: systemPrompt
              }]
            }],
            generationConfig: {
              temperature: 0.3,
              topK: 40,
              topP: 0.95,
              maxOutputTokens: 300,
            }
          })
        });

        if (response.ok) {
          break; // Success, exit the loop
        } else {
          lastError = new Error(`Model ${modelName} failed with status ${response.status}`);
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(`Unknown error with model ${modelName}`);
        console.log(`Failed to call ${modelName}:`, lastError.message);
      }
    }

    if (!response || !response.ok) {
      throw lastError || new Error('All AI models failed to respond');
    }

    const data = await response.json();
    let aiMessage = data.candidates?.[0]?.content?.parts?.[0]?.text || "I am sorry, I could not understand that. Could you please try again?";
    
    // Filter out inappropriate AI responses
    if (aiMessage.toLowerCase().includes('large language model') || 
        aiMessage.toLowerCase().includes('i am an ai') ||
        aiMessage.toLowerCase().includes('google ai website') ||
        aiMessage.toLowerCase().includes('as an ai')) {
      aiMessage = "I am here to help you create your CV. Let us focus on getting your information ready for your job applications.";
    }
    
    // Clean up any remaining formatting
    aiMessage = aiMessage.replace(/^```json\s*/, '').replace(/\s*```$/, '').replace(/^```\s*/, '').replace(/\s*```$/, '');

    // Extract user profile data from the input
    const extractedProfile = extractUserProfile(userInput, conversationState, currentProfile);
    console.log('Extracted profile from user input:', extractedProfile);

    // Determine next state based on completeness
    const nextState = getNextConversationState(conversationState, extractedProfile, currentProfile);

    console.log('Final response - extractedProfile:', extractedProfile);
    console.log('Final response - nextState:', nextState);
    console.log('Final response - currentProfile:', currentProfile);

    return new Response(JSON.stringify({
      message: aiMessage,
      userProfile: extractedProfile,
      nextState: nextState
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in gemini-chat function:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
